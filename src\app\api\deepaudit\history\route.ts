import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session || !session.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const audits = await prisma.deepAudit.findMany({
    where: { userId: session.user.id },
    orderBy: { startedAt: 'desc' },
    select: {
      id: true,
      repoName: true,
      repoOwner: true,
      status: true,
      progress: true,
      startedAt: true,
      completedAt: true,
    },
  });
  return NextResponse.json({ audits });
} 