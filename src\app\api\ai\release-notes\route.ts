import { NextRequest, NextResponse } from 'next/server';
import { openrouterChatCompletion, DEEPSEEK_MODEL, LLAMA4_MAVERICK_MODEL } from '@/lib/deepseek';

const SYSTEM_PROMPT = `You are an expert release manager AI. Given a list of merged pull requests (with titles, descriptions, diffs, and labels), generate clear, concise, and user-friendly release notes or a changelog. Group changes by type (features, bug fixes, improvements, etc.) if possible. Use markdown formatting. Do not include implementation details unless relevant for users. Make the notes suitable for a GitHub release or CHANGELOG.md.`;

export async function POST(req: NextRequest) {
  try {
    const { prs } = await req.json();
    if (!prs || !Array.isArray(prs) || prs.length === 0) {
      return NextResponse.json({ error: 'Missing or empty PRs array' }, { status: 400 });
    }

    // Concatenate PRs for the prompt
    let totalDiffLength = 0;
    const prSummaries = prs.map((pr, idx) => {
      totalDiffLength += pr.diff?.length || 0;
      return `PR #${idx + 1}:
Title: ${pr.title}
Description: ${pr.description}
Labels: ${(pr.labels || []).join(', ')}
Diff: ${pr.diff?.slice(0, 2000) || '[diff omitted]'}\n`;
    }).join('\n---\n');

    // Model selection logic (hybrid)
    const DEEPSEEK_MAX_CHARS = 100000;
    const LLAMA4_MAX_CHARS = 900000;
    let model = DEEPSEEK_MODEL;
    let prSummariesForModel = prSummaries;
    if (totalDiffLength > DEEPSEEK_MAX_CHARS) {
      model = LLAMA4_MAVERICK_MODEL;
    }
    if (model === LLAMA4_MAVERICK_MODEL && totalDiffLength > LLAMA4_MAX_CHARS) {
      // If too large, omit diffs entirely
      prSummariesForModel = prs.map((pr, idx) =>
        `PR #${idx + 1}:
Title: ${pr.title}
Description: ${pr.description}
Labels: ${(pr.labels || []).join(', ')}\n`
      ).join('\n---\n');
    }

    // Try with selected model, fallback if model not found or context error
    try {
      return await generateReleaseNotes(prSummariesForModel, model);
    } catch (err: any) {
      // If DeepSeek fails due to model not found or context, fallback to Llama4
      const errMsg = String(err);
      if (model === DEEPSEEK_MODEL && (errMsg.includes('model') || errMsg.includes('context'))) {
        // Retry with Llama4 Maverick
        try {
          return await generateReleaseNotes(prSummariesForModel, LLAMA4_MAVERICK_MODEL);
        } catch (err2: any) {
          return NextResponse.json({ error: String(err2) }, { status: 500 });
        }
      }
      return NextResponse.json({ error: errMsg }, { status: 500 });
    }
  } catch (error: any) {
    console.error('AI release notes error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

async function generateReleaseNotes(prSummaries: string, model: string) {
  const userPrompt = `Merged Pull Requests:\n${prSummaries}\n\nGenerate release notes for these changes.`;
  const aiResponse = await openrouterChatCompletion(
    [
      { role: 'system', content: SYSTEM_PROMPT },
      { role: 'user', content: userPrompt }
    ],
    model
  );
  return NextResponse.json({ releaseNotes: aiResponse });
} 