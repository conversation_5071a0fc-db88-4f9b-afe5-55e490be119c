import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

const BASE_URL = process.env.NEXTAUTH_URL || 'http://localhost:3000';

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const repositoryId = searchParams.get('repositoryId');
  const pullRequestId = searchParams.get('pullRequestId');
  if (!repositoryId || !pullRequestId) {
    return NextResponse.json({ error: 'Missing parameters' }, { status: 400 });
  }
  const review = await prisma.codeQualityReview.findUnique({
    where: { pullRequestId_repositoryId: { pullRequestId, repositoryId } },
  });
  if (!review) return NextResponse.json({ error: 'Not found' }, { status: 404 });
  // Generate a public URL (could use a hash or just the DB id)
  const url = `${BASE_URL}/share/code-quality-review/${review.id}`;
  return NextResponse.json({ url });
} 