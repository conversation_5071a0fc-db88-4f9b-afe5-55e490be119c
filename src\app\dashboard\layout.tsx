'use client';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { usePathname } from 'next/navigation';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Home,
  GitBranch,
  Activity,
  Settings,
  BarChart3,
  FileText,
  Bell,
  Webhook
} from 'lucide-react';

const navLinks = [
  { href: '/dashboard', label: 'Dashboard Home', icon: Home },
  { href: '/repositories', label: 'Monitored Repositories', icon: GitBranch },
  { href: '/dashboard/pr-activity', label: 'Pull Request Activity', icon: Activity },
  { href: '/dashboard/webhook-setup', label: 'Webhook Setup', icon: Webhook },
  { href: '/dashboard/settings', label: 'Review Settings', icon: Settings },
  { href: '/dashboard/analytics', label: 'Analytics & Insights', icon: BarChart3 },
  { href: '/dashboard/release-notes', label: 'AI-Generated Release Notes', icon: FileText },
];

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const { data: session } = useSession();
  const pathname = usePathname();

  return (
    <div className="min-h-screen flex bg-background">
      {/* Sidebar */}
      <aside className="w-64 bg-card border-r border-border flex flex-col py-6 px-4 shadow-lg">
        {/* User Profile Section */}
        <div className="mb-8 flex flex-col items-center">
          <Avatar className="w-16 h-16 mb-3 border-2 border-primary/20">
            <AvatarImage src={session?.user?.image || ''} alt="avatar" />
            <AvatarFallback className="bg-primary/10 text-primary text-xl font-bold">
              {session?.user?.name?.[0] || 'A'}
            </AvatarFallback>
          </Avatar>
          <h2 className="text-lg font-semibold text-foreground">
            {session?.user?.name || 'Amazingly AI'}
          </h2>
          <p className="text-sm text-muted-foreground">Code Reviewer</p>
        </div>

        {/* Navigation */}
        <nav className="flex-1 space-y-2">
          {navLinks.map((link) => {
            const Icon = link.icon;
            const isActive = pathname === link.href;
            return (
              <Link
                key={link.href}
                href={link.href}
                className={`flex items-center space-x-3 py-3 px-4 rounded-lg transition-all duration-200 group ${
                  isActive
                    ? 'bg-primary text-primary-foreground shadow-md'
                    : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                }`}
              >
                <Icon className={`h-5 w-5 ${isActive ? 'text-primary-foreground' : 'text-muted-foreground group-hover:text-accent-foreground'}`} />
                <span className="font-medium">{link.label}</span>
              </Link>
            );
          })}
        </nav>
        {/* Notification Bell */}
        <div className="mt-8 flex items-center justify-center">
          <Button variant="ghost" size="icon" className="relative group">
            <Bell className="h-5 w-5 text-muted-foreground group-hover:text-primary transition-colors" />
            <span className="absolute -top-1 -right-1 bg-destructive text-destructive-foreground text-xs rounded-full px-1.5 py-0.5">
              2
            </span>
          </Button>
        </div>

        {/* Footer */}
        <div className="mt-6 text-xs text-muted-foreground text-center">
          &copy; {new Date().getFullYear()} Amazingly.dev
        </div>
      </aside>
      {/* Main Content */}
      <div className="flex-1 min-h-screen">
        {children}
      </div>
    </div>
  );
} 