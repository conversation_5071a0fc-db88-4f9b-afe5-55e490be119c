'use client';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { usePathname } from 'next/navigation';

const navLinks = [
  { href: '/dashboard', label: 'Dashboard Home' },
  { href: '/repositories', label: 'Monitored Repositories' },
  { href: '/dashboard/pr-activity', label: 'Pull Request Activity' },
  { href: '/dashboard/webhook-setup', label: 'Webhook Setup' },
  { href: '/dashboard/settings', label: 'Review Settings' },
  { href: '/dashboard/analytics', label: 'Analytics & Insights' },
  { href: '/dashboard/release-notes', label: 'AI-Generated Release Notes' },
];

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const { data: session } = useSession();
  const pathname = usePathname();

  return (
    <div className="min-h-screen flex bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      {/* Sidebar */}
      <aside className="w-64 bg-gray-950 text-white flex flex-col py-8 px-4 shadow-lg">
        <div className="mb-8 flex flex-col items-center">
          {session?.user?.image ? (
            <img src={session.user.image} alt="avatar" className="w-16 h-16 rounded-full mb-2 border-2 border-blue-500" />
          ) : (
            <div className="w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center mb-2 text-2xl font-bold">
              {session?.user?.name?.[0] || 'A'}
            </div>
          )}
          <div className="font-semibold text-lg">{session?.user?.name || 'User'}</div>
          <div className="text-xs text-gray-400">{session?.user?.email}</div>
        </div>
        <nav className="flex-1 space-y-2">
          {navLinks.map(link => (
            <Link
              key={link.href}
              href={link.href}
              className={`block py-2 px-3 rounded transition font-medium ${pathname === link.href ? 'bg-blue-700 text-white' : 'hover:bg-gray-800'}`}
            >
              {link.label}
            </Link>
          ))}
        </nav>
        {/* Notification Bell */}
        <div className="mt-8 flex items-center justify-center">
          <button className="relative group">
            <svg className="w-7 h-7 text-gray-300 group-hover:text-blue-400 transition" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5">2</span>
          </button>
        </div>
        <div className="mt-10 text-xs text-gray-500 text-center">&copy; {new Date().getFullYear()} Amazingly.dev</div>
      </aside>
      {/* Main Content */}
      <div className="flex-1 min-h-screen">
        {children}
      </div>
    </div>
  );
} 