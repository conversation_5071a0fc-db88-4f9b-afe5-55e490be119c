'use client';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { usePathname } from 'next/navigation';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Home,
  GitBranch,
  Activity,
  Settings,
  BarChart3,
  FileText,
  Bell,
  Webhook
} from 'lucide-react';

const navLinks = [
  { href: '/dashboard', label: 'Dashboard Home', icon: Home },
  { href: '/repositories', label: 'Monitored Repositories', icon: GitBranch },
  { href: '/dashboard/pr-activity', label: 'Pull Request Activity', icon: Activity },
  { href: '/dashboard/webhook-setup', label: 'Webhook Setup', icon: Webhook },
  { href: '/dashboard/settings', label: 'Review Settings', icon: Settings },
  { href: '/dashboard/analytics', label: 'Analytics & Insights', icon: BarChart3 },
  { href: '/dashboard/release-notes', label: 'AI-Generated Release Notes', icon: FileText },
];

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const { data: session } = useSession();
  const pathname = usePathname();

  return (
    <div className="min-h-screen flex" style={{backgroundColor: 'var(--background)'}}>
      {/* Sidebar */}
      <aside
        className="w-64 flex flex-col py-6 px-4 shadow-lg"
        style={{
          backgroundColor: 'var(--card)',
          borderRight: '1px solid var(--border)'
        }}
      >
        {/* User Profile Section */}
        <div className="mb-8 flex flex-col items-center">
          <div
            className="w-16 h-16 mb-3 rounded-full flex items-center justify-center text-xl font-bold"
            style={{
              backgroundColor: 'var(--primary)',
              color: 'var(--primary-foreground)',
              border: '2px solid var(--border)'
            }}
          >
            {session?.user?.image ? (
              <img src={session.user.image} alt="avatar" className="w-full h-full rounded-full object-cover" />
            ) : (
              session?.user?.name?.[0] || 'A'
            )}
          </div>
          <h2 className="text-lg font-semibold" style={{color: 'var(--foreground)'}}>
            {session?.user?.name || 'Amazingly AI'}
          </h2>
          <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>Code Reviewer</p>
        </div>

        {/* Navigation */}
        <nav className="flex-1 space-y-2">
          {navLinks.map((link) => {
            const Icon = link.icon;
            const isActive = pathname === link.href;
            return (
              <Link
                key={link.href}
                href={link.href}
                className="flex items-center space-x-3 py-3 px-4 rounded-lg transition-all duration-200 group"
                style={{
                  backgroundColor: isActive ? 'var(--primary)' : 'transparent',
                  color: isActive ? 'var(--primary-foreground)' : 'var(--muted-foreground)'
                }}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.backgroundColor = 'var(--accent)';
                    e.currentTarget.style.color = 'var(--accent-foreground)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = 'var(--muted-foreground)';
                  }
                }}
              >
                <Icon className="h-5 w-5" />
                <span className="font-medium">{link.label}</span>
              </Link>
            );
          })}
        </nav>
        {/* Notification Bell */}
        <div className="mt-8 flex items-center justify-center">
          <button className="relative p-2 rounded-lg transition-colors hover:opacity-80">
            <Bell className="h-5 w-5" style={{color: 'var(--muted-foreground)'}} />
            <span
              className="absolute -top-1 -right-1 text-xs rounded-full px-1.5 py-0.5"
              style={{
                backgroundColor: 'var(--destructive)',
                color: 'var(--destructive-foreground)'
              }}
            >
              2
            </span>
          </button>
        </div>

        {/* Footer */}
        <div className="mt-6 text-xs text-center" style={{color: 'var(--muted-foreground)'}}>
          &copy; {new Date().getFullYear()} Amazingly.dev
        </div>
      </aside>
      {/* Main Content */}
      <div className="flex-1 min-h-screen">
        {children}
      </div>
    </div>
  );
} 