import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

const GITHUB_API = 'https://api.github.com';

export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions);
  const token = session?.accessToken;
  if (!token) {
    return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
  }
  try {
    const { searchParams } = new URL(req.url);
    const owner = searchParams.get('owner');
    const repo = searchParams.get('repo');
    const start = searchParams.get('start');
    const end = searchParams.get('end');
    if (!owner || !repo) {
      return NextResponse.json({ error: 'Missing owner or repo' }, { status: 400 });
    }
    // Fetch merged PRs (state=closed, sort=updated, direction=desc)
    let prs: any[] = [];
    let page = 1;
    let hasMore = true;
    while (hasMore && page <= 5) { // limit to 500 PRs max
      const url = `${GITHUB_API}/repos/${owner}/${repo}/pulls?state=closed&sort=updated&direction=desc&per_page=100&page=${page}`;
      const res = await fetch(url, {
        headers: {
          'Authorization': `token ${token}`,
          'Accept': 'application/vnd.github+json',
        },
      });
      if (!res.ok) {
        const error = await res.text();
        return NextResponse.json({ error }, { status: res.status });
      }
      const data = await res.json();
      const merged = data.filter((pr: any) => pr.merged_at);
      prs = prs.concat(merged);
      hasMore = data.length === 100;
      page++;
    }
    // Filter by date range if provided
    let filtered = prs;
    if (start) {
      filtered = filtered.filter((pr: any) => pr.merged_at && pr.merged_at >= start);
    }
    if (end) {
      filtered = filtered.filter((pr: any) => pr.merged_at && pr.merged_at <= end);
    }
    // Return only needed fields
    const result = filtered.map((pr: any) => ({
      number: pr.number,
      title: pr.title,
      body: pr.body,
      labels: pr.labels,
      merged_at: pr.merged_at,
      base: pr.base,
    }));
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('GitHub merged PRs error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 