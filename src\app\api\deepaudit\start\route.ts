import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { deepseekChatCompletion } from '@/lib/deepseek';
import { prisma } from '@/lib/prisma';

// Helper: Recursively list all files in a GitHub repo, filtering out non-essential dirs/files
async function listFilesInRepo(owner: string, repo: string, accessToken: string, path = ''): Promise<any[]> {
  const url = `https://api.github.com/repos/${owner}/${repo}/contents/${path}`;
  const res = await fetch(url, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
      Accept: 'application/vnd.github+json',
    },
  });
  if (!res.ok) return [];
  const items = await res.json();
  let files: any[] = [];
  for (const item of items) {
    if (item.type === 'dir') {
      // Exclude non-essential dirs
      if ([".git", "node_modules", "dist", "build", "out", ".next", "coverage", "venv", "__pycache__", ".DS_Store", "bin", "obj", "logs", "tmp", "test-results"].includes(item.name)) continue;
      files = files.concat(await listFilesInRepo(owner, repo, accessToken, item.path));
    } else if (item.type === 'file') {
      // Exclude binaries by extension
      if ([".png", ".jpg", ".jpeg", ".gif", ".svg", ".ico", ".exe", ".dll", ".so", ".bin", ".pdf", ".zip", ".tar", ".gz", ".7z", ".mp4", ".mp3", ".mov", ".avi", ".jar", ".class", ".o", ".a", ".dylib", ".ttf", ".woff", ".woff2", ".eot", ".mpg", ".mpeg", ".webm", ".mkv", ".apk", ".app", ".dmg", ".iso"].some(ext => item.name.endsWith(ext))) continue;
      files.push({ path: item.path, size: item.size });
    }
  }
  return files;
}

// Helper: Fetch file content from GitHub
async function fetchFileContent(owner: string, repo: string, path: string, accessToken: string): Promise<string> {
  const url = `https://api.github.com/repos/${owner}/${repo}/contents/${encodeURIComponent(path)}`;
  const res = await fetch(url, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
      Accept: 'application/vnd.github.raw',
    },
  });
  if (!res.ok) return '';
  return await res.text();
}

// Helper: Estimate token count (roughly 1 token ≈ 4 chars for code/text)
function estimateTokens(str: string): number {
  return Math.ceil(str.length / 4);
}

const systemPrompt = `
You are an expert code reviewer. You will be given a chunk of code. Your job is to analyze it and generate a structured, professional audit report. DO NOT repeat or echo the code. Your response MUST strictly follow this structure:

IMPORTANT: All output must be in English. Do not use any other language, even if the code contains non-English comments or strings.
IMPORTANT: Do NOT use Markdown symbols such as #, *, -, etc. Only use bold for section titles, and plain English sentences and paragraphs for all other content.

For each section, use a bold headline (e.g., **Executive Summary**) and then plain text. Do not use lists or bullet points unless necessary for clarity.

**Executive Summary**
Overview: A concise summary highlighting the overall health of the codebase.
Key Metrics: Total files analyzed, number of issues detected, and severity distribution.
Top Concerns: Briefly list the most critical issues found.

**Code Quality Assessment**
Readability & Maintainability: Adherence to coding standards, naming, formatting, dead code.
Complexity Analysis: Cyclomatic complexity, overly complex/lengthy functions.
Duplication Detection: Code duplication, refactoring suggestions.

**Security Analysis**
Vulnerability Detection: Common security issues (SQLi, XSS, CSRF, etc).
Authentication & Authorization: Assessment of auth/access controls.
Data Protection: Encryption and sensitive data handling.

**Performance Evaluation**
Inefficient Code Patterns: Bottlenecks, redundant computations.
Resource Management: Memory/resource utilization.
Database Interactions: Query optimization opportunities.

**Testing & Coverage**
Test Coverage: % covered, untested critical paths.
Test Quality: Effectiveness and reliability of test cases.

**Documentation & Comments**
Code Documentation: Presence/quality of inline comments.
API Documentation: Completeness and clarity.

**Dependency Analysis**
Third-Party Libraries: List, outdated/vulnerable libraries.
License Compliance: License compatibility.

**Architecture & Design**
Modularity: Separation of concerns.
Scalability: Ability to scale with load.
Design Patterns: Appropriateness of patterns used.

**Recommendations**
Prioritized Action Items: Issues sorted by severity/impact.
Refactoring Suggestions: Specific code improvements.
Best Practices: Guidelines to prevent future issues.

**Critical Code Issue (OPTIONAL)**
If you find any block of code that is extremely problematic or dangerous, add a section like this:

**Critical Code Issue**
Description: [Describe the issue]
\`\`\`[language]
[problematic code]
\`\`\`
Recommendation: [How to fix it]

IMPORTANT: You MUST fill out every section, even if you have to write 'No issues found.' or 'Not applicable.' Do NOT leave any section blank. If you cannot analyze, state the reason in the relevant section.`;

export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session || !session.accessToken || !session.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const body = await req.json();
  const { owner, name, id: repositoryId } = body;
  if (!owner || !name || !repositoryId) {
    return NextResponse.json({ error: 'Missing repo info' }, { status: 400 });
  }
  // Create DeepAudit record (pending)
  const audit = await prisma.deepAudit.create({
    data: {
      userId: session.user.id,
      repositoryId: String(repositoryId),
      repoName: name,
      repoOwner: owner,
      status: 'pending',
      progress: 0,
    },
  });

  // Start background processing (simulate with setTimeout for now)
  setTimeout(async () => {
    try {
      if (!owner || !name || !session.accessToken) throw new Error('Missing repo info or access token');
      // List all files (filtered)
      const files = await listFilesInRepo(owner as string, name as string, session.accessToken as string);
      // Group files by top-level directory/module
      const grouped: Record<string, any[]> = {};
      for (const file of files) {
        const parts = file.path.split('/');
        const group = parts.length > 1 ? parts[0] : 'root';
        if (!grouped[group]) grouped[group] = [];
        grouped[group].push(file);
      }
      // Chunking logic (token-based)
      const MAX_TOKENS_PER_CHUNK = 20000; // ~80,000 chars, well below model limit
      let chunks: any[] = [];
      let currentChunk: any[] = [];
      let currentTokens = 0;
      for (const file of files) {
        if (file.size > 200 * 1024) continue; // skip huge files
        // Fetch content to estimate tokens
        const content = await fetchFileContent(owner as string, name as string, file.path, session.accessToken as string);
        const fileTokens = estimateTokens(content);
        // If file itself is too large, skip and warn
        if (fileTokens > MAX_TOKENS_PER_CHUNK) {
          chunks.push({ files: [file], totalTokens: fileTokens, warning: `File ${file.path} is too large to audit in one chunk and was skipped.` });
          continue;
        }
        if (currentTokens + fileTokens > MAX_TOKENS_PER_CHUNK && currentChunk.length > 0) {
          chunks.push({ files: currentChunk, totalTokens: currentTokens });
          currentChunk = [];
          currentTokens = 0;
        }
        currentChunk.push({ ...file, content });
        currentTokens += fileTokens;
      }
      if (currentChunk.length > 0) {
        chunks.push({ files: currentChunk, totalTokens: currentTokens });
      }
      // For each chunk, call DeepSeek V3 0324 (deepseek/deepseek-chat-v3-0324, 163,840 context, 685B params)
      const chunkResults: any[] = [];
      for (let i = 0; i < chunks.length; ++i) {
        const chunk = chunks[i];
        let chunkContent = '';
        let warning = chunk.warning || '';
        for (const file of chunk.files) {
          if (file.content) {
            chunkContent += `\n// File: ${file.path}\n` + file.content;
          }
        }
        let aiSummary = '';
        if (warning) {
          aiSummary = warning;
        } else if (chunkContent.trim().length > 0) {
          try {
            aiSummary = await deepseekChatCompletion([
              {
                role: 'system',
                content: systemPrompt
              },
              {
                role: 'user',
                content: `Please analyze the following code according to the structure above. Do NOT repeat the code. Only provide your analysis.\n--- BEGIN CODE ---\n${chunkContent}\n--- END CODE ---`
              }
            ]);
          } catch (e) {
            aiSummary = 'DeepSeek analysis failed: ' + (e as any)?.message;
            // Mark this chunk as failed
            chunkResults.push({
              chunkIndex: i + 1,
              fileCount: chunk.files.length,
              totalTokens: chunk.totalTokens,
              summary: aiSummary,
              failed: true
            });
            // Update progress
            await prisma.deepAudit.update({
              where: { id: audit.id },
              data: { progress: ((i + 1) / chunks.length) * 100, chunkResults },
            });
            continue;
          }
        } else {
          aiSummary = 'No code content in this chunk.';
        }
        chunkResults.push({
          chunkIndex: i + 1,
          fileCount: chunk.files.length,
          totalTokens: chunk.totalTokens,
          summary: aiSummary,
        });
        // Update progress
        await prisma.deepAudit.update({
          where: { id: audit.id },
          data: { progress: ((i + 1) / chunks.length) * 100, chunkResults },
        });
      }
      // If any chunk failed, mark audit as failed and show error
      if (chunkResults.some(r => r.failed)) {
        await prisma.deepAudit.update({
          where: { id: audit.id },
          data: { status: 'failed', error: 'One or more chunks failed DeepSeek analysis. Please try a smaller repo or contact support.', chunkResults, progress: 100 },
        });
        return;
      }
      // Synthesize a unified report using DeepSeek V3
      let unifiedReportText = '';
      try {
        const allSummaries = chunkResults.map((c: any, idx: number) => `Chunk ${idx + 1} Report:\n${c.summary}`).join('\n\n');
        const synthPrompt = `You are an expert code auditor. You will be given multiple chunked audit reports for different parts of a codebase. Your job is to COMBINE these into a single, comprehensive, non-redundant, sectioned audit report for the entire codebase.\n\nIMPORTANT: Do NOT mention chunks or chunk numbers. Do NOT repeat content. Synthesize a holistic, project-wide report.\n\nUse the following structure (with bold section titles, no Markdown symbols except bold, and plain English):\n\n**Executive Summary**\n**Code Quality Assessment**\n**Security Analysis**\n**Performance Evaluation**\n**Testing & Coverage**\n**Documentation & Comments**\n**Dependency Analysis**\n**Architecture & Design**\n**Recommendations**\n\nIf there are any critical code issues, add a section at the end titled **Critical Code Issue**.\n\nHere are the chunked audit reports:\n\n${allSummaries}`;
        unifiedReportText = await deepseekChatCompletion([
          { role: 'system', content: systemPrompt },
          { role: 'user', content: synthPrompt }
        ]);
      } catch (e) {
        unifiedReportText = 'Failed to synthesize unified report: ' + (e as any)?.message;
      }
      // Parse the unified report into sections
      function parseSections(text: string): Record<string, string> & { criticalIssues: string[] } {
        const sectionTitles = [
          'Executive Summary',
          'Code Quality Assessment',
          'Security Analysis',
          'Performance Evaluation',
          'Testing & Coverage',
          'Documentation & Comments',
          'Dependency Analysis',
          'Architecture & Design',
          'Recommendations',
        ];
        const regex = /\*\*(.+?)\*\*\n([\s\S]*?)(?=\*\*|$)/g;
        let match;
        const sections: Record<string, string> = {};
        while ((match = regex.exec(text)) !== null) {
          const title = match[1].trim();
          const content = match[2].trim();
          sections[title] = content;
        }
        // Parse any Critical Code Issue blocks
        const criticalRegex = /\*\*Critical Code Issue\*\*\n([\s\S]*?)(?=\*\*|$)/g;
        let criticalIssues: string[] = [];
        let critMatch;
        while ((critMatch = criticalRegex.exec(text)) !== null) {
          criticalIssues.push(critMatch[1].trim());
        }
        // Ensure all sections are present
        for (const title of sectionTitles) {
          if (!sections[title]) sections[title] = 'No issues found.';
        }
        return Object.assign(sections, { criticalIssues }) as Record<string, string> & { criticalIssues: string[] };
      }
      const unifiedSections = parseSections(unifiedReportText);
      const report = {
        summary: 'Comprehensive DeepAudit Report',
        totalFiles: files.length,
        totalChunks: chunks.length,
        findings: unifiedReportText,
        structure: 'Executive Summary, Code Quality Assessment, Security Analysis, Performance Evaluation, Testing & Coverage, Documentation & Comments, Dependency Analysis, Architecture & Design, Recommendations',
        sections: unifiedSections,
        criticalIssues: unifiedSections.criticalIssues,
      };
      await prisma.deepAudit.update({
        where: { id: audit.id },
        data: { status: 'completed', completedAt: new Date(), report, chunkResults, progress: 100 },
      });
    } catch (e: any) {
      await prisma.deepAudit.update({
        where: { id: audit.id },
        data: { status: 'failed', error: e.message },
      });
    }
  }, 100);

  // Return auditId immediately
  return NextResponse.json({ auditId: audit.id });
} 