// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id                String   @id @default(cuid())
  name              String?
  email             String?  @unique
  emailVerified     DateTime?
  image             String?
  accounts          Account[]
  sessions          Session[]
  monitoredRepos    MonitoredRepository[]
  reviewSettings    ReviewSettings?
  reviewStats       ReviewStats[]
  deepAudits        DeepAudit[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model MonitoredRepository {
  id        String   @id @default(cuid())
  userId    String
  repoId    String
  repoName  String
  repoOwner String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  qualityScores CodeQualityScore[]

  @@unique([userId, repoId])
  @@index([userId])
}

model ReviewStats {
  id                String   @id @default(cuid())
  userId            String
  repositoryId      String
  totalReviews      Int      @default(0)
  bugsFound         Int      @default(0)
  securityIssues    Int      @default(0)
  performanceIssues Int      @default(0)
  maintainabilityIssues Int  @default(0)
  averageReviewTime Int      @default(0) // in minutes
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, repositoryId])
  @@index([userId])
}

model ReviewSettings {
  id                        String   @id @default(cuid())
  userId                    String   @unique
  user                      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  codeStyleWeight           Float    @default(1.0)
  securityWeight            Float    @default(1.0)
  performanceWeight         Float    @default(1.0)
  maintainabilityWeight     Float    @default(1.0)
  enableInlineComments      Boolean  @default(true)
  enableSummaryComments     Boolean  @default(true)
  enableBugDetection        Boolean  @default(true)
  enableSecurityChecks      Boolean  @default(true)
  enablePerformanceChecks   Boolean  @default(true)
  enableMaintainabilityChecks Boolean @default(true)
  enableNotifications       Boolean  @default(true)
  createdAt                 DateTime @default(now())
  updatedAt                 DateTime @updatedAt

  @@index([userId])
}

model CodeQualityScore {
  id                String   @id @default(cuid())
  pullRequestId     String
  repositoryId      String
  score             Float    // 0-100 scale
  codeComplexity    Float    // 0-100 scale
  testCoverage      Float    // 0-100 scale
  codeStyle         Float    // 0-100 scale
  securityScore     Float    // 0-100 scale
  documentationScore Float   // 0-100 scale
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  repository        MonitoredRepository @relation(fields: [repositoryId], references: [id], onDelete: Cascade)

  @@unique([pullRequestId])
  @@index([repositoryId])
  @@index([createdAt])
}

model PullRequestReview {
  id            String   @id @default(cuid())
  pullRequestId String
  repositoryId  String
  review        String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@unique([pullRequestId, repositoryId])
  @@index([repositoryId])
}

model CodeQualityReview {
  id            String   @id @default(cuid())
  pullRequestId String
  repositoryId  String
  review        Json     // Structured review (array of issues/tips)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@unique([pullRequestId, repositoryId])
  @@index([repositoryId])
}

model DeepAudit {
  id           String   @id @default(cuid())
  userId       String
  repositoryId String
  repoName     String
  repoOwner    String
  status       String   // pending, running, completed, failed
  startedAt    DateTime @default(now())
  completedAt  DateTime?
  progress     Float    @default(0)
  report       Json?
  chunkResults Json?
  error        String?
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
} 