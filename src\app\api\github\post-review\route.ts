import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

// Helper to parse AI output for inline comments
function parseInlineComments(aiReview: string) {
  // Format: [File: <filename> | Line: <line number>]\nComment: <your comment>
  const regex = /\[File: ([^|]+) \| Line: (\d+)\]\s*Comment: ([^\n]+)/g;
  const comments = [];
  let match;
  while ((match = regex.exec(aiReview)) !== null) {
    comments.push({
      path: match[1].trim(),
      line: parseInt(match[2], 10),
      body: match[3].trim(),
    });
  }
  return comments;
}

export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session || !session.accessToken) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    const { owner, repo, number, body } = await req.json();
    if (!owner || !repo || !number || !body) {
      return NextResponse.json({ error: 'Missing parameters' }, { status: 400 });
    }
    // Parse inline comments from AI review
    const inlineComments = parseInlineComments(body);
    if (inlineComments.length > 0) {
      // Post as a GitHub review with comments
      const reviewComments = inlineComments.map(c => ({
        path: c.path,
        body: c.body,
        line: c.line,
        side: 'RIGHT',
      }));
      const res = await fetch(`https://api.github.com/repos/${owner}/${repo}/pulls/${number}/reviews`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${session.accessToken}`,
          Accept: 'application/vnd.github+json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          body: 'Automated AI Review:\n\n' + body,
          event: 'COMMENT',
          comments: reviewComments,
        }),
      });
      if (!res.ok) {
        const error = await res.text();
        return NextResponse.json({ error: error || 'Failed to post review.' }, { status: 500 });
      }
      return NextResponse.json({ success: true, type: 'inline' });
    } else {
      // Fallback: post as a summary comment
      const res = await fetch(`https://api.github.com/repos/${owner}/${repo}/issues/${number}/comments`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${session.accessToken}`,
          Accept: 'application/vnd.github+json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ body }),
      });
      if (!res.ok) {
        const error = await res.text();
        return NextResponse.json({ error: error || 'Failed to post comment.' }, { status: 500 });
      }
      return NextResponse.json({ success: true, type: 'summary' });
    }
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 