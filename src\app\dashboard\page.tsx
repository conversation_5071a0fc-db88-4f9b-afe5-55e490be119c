'use client';

import { useState, useEffect } from 'react';
import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import {
  GitBranch,
  Activity,
  Settings,
  BarChart3,
  FileText,
  Zap,
  ArrowRight,
  Shield,
  Clock,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';

// Simple Card Component
const SimpleCard = ({ children, className = "", style = {} }: { 
  children: React.ReactNode, 
  className?: string, 
  style?: React.CSSProperties 
}) => (
  <div
    className={`rounded-lg p-6 shadow-sm transition-all hover:shadow-lg ${className}`}
    style={{
      backgroundColor: 'var(--card)',
      border: '1px solid var(--border)',
      color: 'var(--card-foreground)',
      ...style
    }}
  >
    {children}
  </div>
);

// Simple Button Component
const SimpleButton = ({ 
  children, 
  onClick, 
  className = "", 
  style = {},
  disabled = false 
}: { 
  children: React.ReactNode, 
  onClick?: () => void, 
  className?: string, 
  style?: React.CSSProperties,
  disabled?: boolean 
}) => (
  <button
    onClick={onClick}
    disabled={disabled}
    className={`px-4 py-2 rounded-lg font-medium transition-all hover:opacity-90 flex items-center justify-center space-x-2 ${className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    style={{
      backgroundColor: 'var(--primary)',
      color: 'var(--primary-foreground)',
      ...style
    }}
  >
    {children}
  </button>
);

interface DashboardStats {
  monitoredRepos: number;
  totalPullRequests: number;
  totalDeepAudits: number;
  totalIssuesFound: number;
}

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [stats, setStats] = useState<DashboardStats>({
    monitoredRepos: 0,
    totalPullRequests: 0,
    totalDeepAudits: 0,
    totalIssuesFound: 0,
  });
  const [loading, setLoading] = useState(true);

  // Fetch dashboard statistics
  const fetchStats = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/dashboard/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (session) {
      fetchStats();
    }
  }, [session]);

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{backgroundColor: 'var(--background)'}}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style={{borderColor: 'var(--primary)'}}></div>
          <div style={{color: 'var(--foreground)'}}>Loading dashboard...</div>
        </div>
      </div>
    );
  }

  if (!session) {
    if (typeof window !== 'undefined') router.push('/auth/signin');
    return null;
  }

  return (
    <main className="p-6" style={{backgroundColor: 'var(--background)', minHeight: '100vh'}}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <header className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold mb-2" style={{color: 'var(--foreground)'}}>
              Welcome back, {session.user?.name || 'Developer'}!
            </h1>
            <p className="text-lg" style={{color: 'var(--muted-foreground)'}}>
              Manage your AI-powered code reviews and repository monitoring
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={fetchStats}
              disabled={loading}
              className="px-4 py-2 rounded-lg shadow-lg font-medium transition-colors hover:opacity-90 flex items-center space-x-2"
              style={{
                backgroundColor: 'var(--secondary)',
                color: 'var(--secondary-foreground)'
              }}
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
            <button
              onClick={() => signOut()}
              className="px-4 py-2 rounded-lg shadow-lg font-medium transition-colors hover:opacity-90"
              style={{
                backgroundColor: 'var(--destructive)',
                color: 'var(--destructive-foreground)'
              }}
            >
              Sign Out
            </button>
          </div>
        </header>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <SimpleCard>
            <div className="flex flex-row items-center justify-between pb-2">
              <h3 className="text-sm font-medium" style={{color: 'var(--muted-foreground)'}}>
                Monitored Repositories
              </h3>
              <GitBranch className="h-4 w-4" style={{color: 'var(--muted-foreground)'}} />
            </div>
            <div>
              <div className="text-2xl font-bold" style={{color: 'var(--foreground)'}}>
                {loading ? '...' : stats.monitoredRepos}
              </div>
              <p className="text-xs" style={{color: 'var(--muted-foreground)'}}>
                {stats.monitoredRepos === 0 ? 'No repositories monitored' : 'Currently being monitored'}
              </p>
            </div>
          </SimpleCard>

          <SimpleCard>
            <div className="flex flex-row items-center justify-between pb-2">
              <h3 className="text-sm font-medium" style={{color: 'var(--muted-foreground)'}}>
                Total Pull Requests
              </h3>
              <Activity className="h-4 w-4" style={{color: 'var(--muted-foreground)'}} />
            </div>
            <div>
              <div className="text-2xl font-bold" style={{color: 'var(--foreground)'}}>
                {loading ? '...' : stats.totalPullRequests}
              </div>
              <p className="text-xs" style={{color: 'var(--muted-foreground)'}}>
                {stats.totalPullRequests === 0 ? 'No reviews yet' : 'Reviews completed'}
              </p>
            </div>
          </SimpleCard>

          <SimpleCard>
            <div className="flex flex-row items-center justify-between pb-2">
              <h3 className="text-sm font-medium" style={{color: 'var(--muted-foreground)'}}>
                Deep Audits Run
              </h3>
              <Zap className="h-4 w-4" style={{color: 'var(--muted-foreground)'}} />
            </div>
            <div>
              <div className="text-2xl font-bold" style={{color: 'var(--foreground)'}}>
                {loading ? '...' : stats.totalDeepAudits}
              </div>
              <p className="text-xs" style={{color: 'var(--muted-foreground)'}}>
                {stats.totalDeepAudits === 0 ? 'No audits yet' : 'Comprehensive audits'}
              </p>
            </div>
          </SimpleCard>

          <SimpleCard>
            <div className="flex flex-row items-center justify-between pb-2">
              <h3 className="text-sm font-medium" style={{color: 'var(--muted-foreground)'}}>
                Total Issues Found
              </h3>
              <AlertTriangle className="h-4 w-4" style={{color: 'var(--muted-foreground)'}} />
            </div>
            <div>
              <div className="text-2xl font-bold" style={{color: 'var(--foreground)'}}>
                {loading ? '...' : stats.totalIssuesFound}
              </div>
              <p className="text-xs" style={{color: 'var(--muted-foreground)'}}>
                {stats.totalIssuesFound === 0 ? 'No issues detected' : 'Issues identified & fixed'}
              </p>
            </div>
          </SimpleCard>
        </div>

        {/* Getting Started Message */}
        {!loading && stats.monitoredRepos === 0 && (
          <div
            className="rounded-lg p-6 mb-8 text-center"
            style={{
              backgroundColor: 'var(--card)',
              border: '1px solid var(--border)',
              color: 'var(--card-foreground)'
            }}
          >
            <GitBranch className="h-12 w-12 mx-auto mb-4" style={{color: 'var(--muted-foreground)'}} />
            <h3 className="text-xl font-semibold mb-2" style={{color: 'var(--foreground)'}}>
              Get Started with AI Code Reviews
            </h3>
            <p className="text-sm mb-4" style={{color: 'var(--muted-foreground)'}}>
              Start by selecting repositories to monitor. Once you have repositories set up, you'll see real-time statistics here.
            </p>
            <SimpleButton onClick={() => router.push('/repositories')}>
              <GitBranch className="h-4 w-4" />
              <span>Select Repositories to Monitor</span>
            </SimpleButton>
          </div>
        )}

        {/* Main Feature Cards */}
        <section className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Manage Repositories */}
          <SimpleCard>
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-3">
                <GitBranch className="h-6 w-6" style={{color: 'var(--primary)'}} />
                <h3 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                  Monitored Repositories
                </h3>
              </div>
              <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                Select which GitHub repositories you want to monitor for AI-powered code reviews.
              </p>
            </div>
            <SimpleButton onClick={() => router.push('/repositories')} className="w-full">
              <span>Manage Repositories</span>
              <ArrowRight className="h-4 w-4" />
            </SimpleButton>
          </SimpleCard>

          {/* PR Activity */}
          <SimpleCard>
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-3">
                <Activity className="h-6 w-6 text-purple-400" />
                <h3 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                  Pull Request Activity
                </h3>
              </div>
              <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                View recent pull requests and AI review status for your monitored repositories.
              </p>
            </div>
            <SimpleButton 
              onClick={() => router.push('/dashboard/pr-activity')} 
              className="w-full"
              style={{backgroundColor: 'var(--secondary)', color: 'var(--secondary-foreground)'}}
            >
              <span>View PR Activity</span>
              <ArrowRight className="h-4 w-4" />
            </SimpleButton>
          </SimpleCard>

          {/* Webhook Setup */}
          <SimpleCard>
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-3">
                <Settings className="h-6 w-6 text-green-400" />
                <h3 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                  Webhook Setup
                </h3>
              </div>
              <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                Configure automatic AI reviews for your repositories using GitHub webhooks.
              </p>
            </div>
            <SimpleButton 
              onClick={() => router.push('/dashboard/webhook-setup')} 
              className="w-full"
              style={{backgroundColor: 'var(--accent)', color: 'var(--accent-foreground)'}}
            >
              <span>Setup Webhooks</span>
              <ArrowRight className="h-4 w-4" />
            </SimpleButton>
          </SimpleCard>

          {/* Review Settings */}
          <SimpleCard>
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-3">
                <Settings className="h-6 w-6 text-orange-400" />
                <h3 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                  Review Settings
                </h3>
              </div>
              <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                Customize your AI review preferences and notification settings.
              </p>
            </div>
            <SimpleButton 
              onClick={() => router.push('/dashboard/settings')} 
              className="w-full"
              style={{backgroundColor: 'var(--accent)', color: 'var(--accent-foreground)'}}
            >
              <span>Configure Settings</span>
              <ArrowRight className="h-4 w-4" />
            </SimpleButton>
          </SimpleCard>

          {/* Analytics */}
          <SimpleCard>
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-3">
                <BarChart3 className="h-6 w-6 text-blue-400" />
                <h3 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                  Analytics & Insights
                </h3>
              </div>
              <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                Track code quality improvements and review statistics over time.
              </p>
            </div>
            <SimpleButton disabled className="w-full">
              <span>Coming Soon</span>
              <Clock className="h-4 w-4" />
            </SimpleButton>
          </SimpleCard>

          {/* DeepAudit - Featured Card */}
          <SimpleCard 
            style={{
              background: 'linear-gradient(135deg, #0c4a6e 0%, #1e3a8a 100%)',
              border: '1px solid #0ea5e9'
            }}
          >
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-3">
                <Zap className="h-6 w-6 text-cyan-400" />
                <h3 className="text-xl font-semibold text-cyan-100">DeepAudit</h3>
                <span className="px-2 py-1 text-xs bg-cyan-400/20 text-cyan-300 rounded-full">
                  AI-Powered
                </span>
              </div>
              <p className="text-sm text-cyan-200/80">
                Run a comprehensive, step-by-step AI audit on any of your repositories. Get detailed reports on code quality, security, performance, and more.
              </p>
            </div>
            <SimpleButton 
              onClick={() => router.push('/deepaudit')} 
              className="w-full"
              style={{backgroundColor: '#0891b2', color: 'white'}}
            >
              <span>Go to DeepAudit</span>
              <Zap className="h-4 w-4" />
            </SimpleButton>
          </SimpleCard>
        </section>
      </div>
    </main>
  );
}
