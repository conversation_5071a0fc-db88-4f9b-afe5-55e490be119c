'use client';

import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  if (status === 'loading') {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }

  if (!session) {
    if (typeof window !== 'undefined') router.push('/auth/signin');
    return null;
  }

  return (
    <main className="p-8">
      <div className="max-w-5xl mx-auto">
        <header className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-1">Welcome, {session.user?.name || 'Developer'}!</h1>
            <p className="text-gray-600">Amazingly AI Code Reviewer Dashboard</p>
          </div>
          <button
            onClick={() => signOut()}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 shadow"
          >
            Sign Out
          </button>
        </header>

        <section className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Manage Repositories */}
          <div className="bg-white rounded-lg shadow p-6 flex flex-col justify-between">
            <div>
              <h2 className="text-2xl font-semibold mb-2">Monitored Repositories</h2>
              <p className="text-gray-600 mb-4">Select which GitHub repositories you want to monitor for AI-powered code reviews.</p>
            </div>
            <button
              onClick={() => router.push('/repositories')}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 mt-2"
            >
              Manage Repositories
            </button>
          </div>

          {/* PR Activity */}
          <div className="bg-white rounded-lg shadow p-6 flex flex-col justify-between">
            <div>
              <h2 className="text-2xl font-semibold mb-2">Pull Request Activity</h2>
              <p className="text-gray-600 mb-4">View recent pull requests and AI review status for your monitored repositories.</p>
            </div>
            <button
              onClick={() => router.push('/dashboard/pr-activity')}
              className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 mt-2"
            >
              View PR Activity
            </button>
          </div>

          {/* Webhook Setup */}
          <div className="bg-white rounded-lg shadow p-6 flex flex-col justify-between">
            <div>
              <h2 className="text-2xl font-semibold mb-2">Webhook Setup</h2>
              <p className="text-gray-600 mb-4">Configure automatic AI reviews for your repositories using GitHub webhooks.</p>
            </div>
            <button
              onClick={() => router.push('/dashboard/webhook-setup')}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 mt-2"
            >
              Setup Webhooks
            </button>
          </div>

          {/* Review Settings */}
          <div className="bg-white rounded-lg shadow p-6 flex flex-col justify-between">
            <div>
              <h2 className="text-2xl font-semibold mb-2">Review Settings</h2>
              <p className="text-gray-600 mb-4">Customize your AI review preferences and notification settings.</p>
            </div>
            <button
              onClick={() => router.push('/dashboard/settings')}
              className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 mt-2"
            >
              Configure Settings
            </button>
          </div>

          {/* Analytics */}
          <div className="bg-white rounded-lg shadow p-6 flex flex-col justify-between">
            <div>
              <h2 className="text-2xl font-semibold mb-2">Analytics & Insights</h2>
              <p className="text-gray-600 mb-4">Track code quality improvements and review statistics over time.</p>
            </div>
            <button
              disabled
              className="bg-gray-400 text-white px-4 py-2 rounded mt-2 cursor-not-allowed"
            >
              Coming Soon
            </button>
          </div>

          {/* DeepAudit */}
          <div className="bg-white rounded-lg shadow p-6 flex flex-col justify-between border-2 border-cyan-600">
            <div>
              <h2 className="text-2xl font-semibold mb-2 text-cyan-700">DeepAudit (AI-Powered Full Repo Audit)</h2>
              <p className="text-gray-600 mb-4">Run a comprehensive, step-by-step AI audit on any of your repositories. Get a detailed report on code quality, security, performance, and more.</p>
            </div>
            <button
              onClick={() => router.push('/deepaudit')}
              className="bg-cyan-600 text-white px-4 py-2 rounded hover:bg-cyan-700 mt-2"
            >
              Go to DeepAudit
            </button>
          </div>
        </section>
      </div>
    </main>
  );
} 