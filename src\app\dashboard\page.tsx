'use client';

import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { 
  GitBranch, 
  Activity, 
  Settings, 
  BarChart3, 
  FileText, 
  Zap,
  ArrowRight,
  Shield,
  Clock
} from 'lucide-react';

// Simple Card Component
const SimpleCard = ({ children, className = "", style = {} }: { 
  children: React.ReactNode, 
  className?: string, 
  style?: React.CSSProperties 
}) => (
  <div
    className={`rounded-lg p-6 shadow-sm transition-all hover:shadow-lg ${className}`}
    style={{
      backgroundColor: 'var(--card)',
      border: '1px solid var(--border)',
      color: 'var(--card-foreground)',
      ...style
    }}
  >
    {children}
  </div>
);

// Simple Button Component
const SimpleButton = ({ 
  children, 
  onClick, 
  className = "", 
  style = {},
  disabled = false 
}: { 
  children: React.ReactNode, 
  onClick?: () => void, 
  className?: string, 
  style?: React.CSSProperties,
  disabled?: boolean 
}) => (
  <button
    onClick={onClick}
    disabled={disabled}
    className={`px-4 py-2 rounded-lg font-medium transition-all hover:opacity-90 flex items-center justify-center space-x-2 ${className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    style={{
      backgroundColor: 'var(--primary)',
      color: 'var(--primary-foreground)',
      ...style
    }}
  >
    {children}
  </button>
);

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{backgroundColor: 'var(--background)'}}>
        <div style={{color: 'var(--foreground)'}}>Loading...</div>
      </div>
    );
  }

  if (!session) {
    if (typeof window !== 'undefined') router.push('/auth/signin');
    return null;
  }

  return (
    <main className="p-6" style={{backgroundColor: 'var(--background)', minHeight: '100vh'}}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <header className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold mb-2" style={{color: 'var(--foreground)'}}>
              Welcome back, {session.user?.name || 'Developer'}!
            </h1>
            <p className="text-lg" style={{color: 'var(--muted-foreground)'}}>
              Manage your AI-powered code reviews and repository monitoring
            </p>
          </div>
          <button 
            onClick={() => signOut()}
            className="px-4 py-2 rounded-lg shadow-lg font-medium transition-colors hover:opacity-90"
            style={{
              backgroundColor: 'var(--destructive)',
              color: 'var(--destructive-foreground)'
            }}
          >
            Sign Out
          </button>
        </header>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <SimpleCard>
            <div className="flex flex-row items-center justify-between pb-2">
              <h3 className="text-sm font-medium" style={{color: 'var(--muted-foreground)'}}>
                Active Repositories
              </h3>
              <GitBranch className="h-4 w-4" style={{color: 'var(--muted-foreground)'}} />
            </div>
            <div>
              <div className="text-2xl font-bold" style={{color: 'var(--foreground)'}}>12</div>
              <p className="text-xs" style={{color: 'var(--muted-foreground)'}}>
                <span className="text-green-400">+2</span> from last month
              </p>
            </div>
          </SimpleCard>

          <SimpleCard>
            <div className="flex flex-row items-center justify-between pb-2">
              <h3 className="text-sm font-medium" style={{color: 'var(--muted-foreground)'}}>
                Reviews This Week
              </h3>
              <Activity className="h-4 w-4" style={{color: 'var(--muted-foreground)'}} />
            </div>
            <div>
              <div className="text-2xl font-bold" style={{color: 'var(--foreground)'}}>47</div>
              <p className="text-xs" style={{color: 'var(--muted-foreground)'}}>
                <span className="text-green-400">+12%</span> from last week
              </p>
            </div>
          </SimpleCard>

          <SimpleCard>
            <div className="flex flex-row items-center justify-between pb-2">
              <h3 className="text-sm font-medium" style={{color: 'var(--muted-foreground)'}}>
                Issues Found
              </h3>
              <Shield className="h-4 w-4" style={{color: 'var(--muted-foreground)'}} />
            </div>
            <div>
              <div className="text-2xl font-bold" style={{color: 'var(--foreground)'}}>23</div>
              <p className="text-xs" style={{color: 'var(--muted-foreground)'}}>
                <span className="text-red-400">+3</span> critical issues
              </p>
            </div>
          </SimpleCard>

          <SimpleCard>
            <div className="flex flex-row items-center justify-between pb-2">
              <h3 className="text-sm font-medium" style={{color: 'var(--muted-foreground)'}}>
                Avg Review Time
              </h3>
              <Clock className="h-4 w-4" style={{color: 'var(--muted-foreground)'}} />
            </div>
            <div>
              <div className="text-2xl font-bold" style={{color: 'var(--foreground)'}}>2.4m</div>
              <p className="text-xs" style={{color: 'var(--muted-foreground)'}}>
                <span className="text-green-400">-0.3m</span> faster than usual
              </p>
            </div>
          </SimpleCard>
        </div>

        {/* Main Feature Cards */}
        <section className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Manage Repositories */}
          <SimpleCard>
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-3">
                <GitBranch className="h-6 w-6" style={{color: 'var(--primary)'}} />
                <h3 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                  Monitored Repositories
                </h3>
              </div>
              <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                Select which GitHub repositories you want to monitor for AI-powered code reviews.
              </p>
            </div>
            <SimpleButton onClick={() => router.push('/repositories')} className="w-full">
              <span>Manage Repositories</span>
              <ArrowRight className="h-4 w-4" />
            </SimpleButton>
          </SimpleCard>

          {/* PR Activity */}
          <SimpleCard>
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-3">
                <Activity className="h-6 w-6 text-purple-400" />
                <h3 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                  Pull Request Activity
                </h3>
              </div>
              <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                View recent pull requests and AI review status for your monitored repositories.
              </p>
            </div>
            <SimpleButton 
              onClick={() => router.push('/dashboard/pr-activity')} 
              className="w-full"
              style={{backgroundColor: 'var(--secondary)', color: 'var(--secondary-foreground)'}}
            >
              <span>View PR Activity</span>
              <ArrowRight className="h-4 w-4" />
            </SimpleButton>
          </SimpleCard>

          {/* Webhook Setup */}
          <SimpleCard>
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-3">
                <Settings className="h-6 w-6 text-green-400" />
                <h3 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                  Webhook Setup
                </h3>
              </div>
              <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                Configure automatic AI reviews for your repositories using GitHub webhooks.
              </p>
            </div>
            <SimpleButton 
              onClick={() => router.push('/dashboard/webhook-setup')} 
              className="w-full"
              style={{backgroundColor: 'var(--accent)', color: 'var(--accent-foreground)'}}
            >
              <span>Setup Webhooks</span>
              <ArrowRight className="h-4 w-4" />
            </SimpleButton>
          </SimpleCard>

          {/* Review Settings */}
          <SimpleCard>
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-3">
                <Settings className="h-6 w-6 text-orange-400" />
                <h3 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                  Review Settings
                </h3>
              </div>
              <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                Customize your AI review preferences and notification settings.
              </p>
            </div>
            <SimpleButton 
              onClick={() => router.push('/dashboard/settings')} 
              className="w-full"
              style={{backgroundColor: 'var(--accent)', color: 'var(--accent-foreground)'}}
            >
              <span>Configure Settings</span>
              <ArrowRight className="h-4 w-4" />
            </SimpleButton>
          </SimpleCard>

          {/* Analytics */}
          <SimpleCard>
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-3">
                <BarChart3 className="h-6 w-6 text-blue-400" />
                <h3 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                  Analytics & Insights
                </h3>
              </div>
              <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                Track code quality improvements and review statistics over time.
              </p>
            </div>
            <SimpleButton disabled className="w-full">
              <span>Coming Soon</span>
              <Clock className="h-4 w-4" />
            </SimpleButton>
          </SimpleCard>

          {/* DeepAudit - Featured Card */}
          <SimpleCard 
            style={{
              background: 'linear-gradient(135deg, #0c4a6e 0%, #1e3a8a 100%)',
              border: '1px solid #0ea5e9'
            }}
          >
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-3">
                <Zap className="h-6 w-6 text-cyan-400" />
                <h3 className="text-xl font-semibold text-cyan-100">DeepAudit</h3>
                <span className="px-2 py-1 text-xs bg-cyan-400/20 text-cyan-300 rounded-full">
                  AI-Powered
                </span>
              </div>
              <p className="text-sm text-cyan-200/80">
                Run a comprehensive, step-by-step AI audit on any of your repositories. Get detailed reports on code quality, security, performance, and more.
              </p>
            </div>
            <SimpleButton 
              onClick={() => router.push('/deepaudit')} 
              className="w-full"
              style={{backgroundColor: '#0891b2', color: 'white'}}
            >
              <span>Go to DeepAudit</span>
              <Zap className="h-4 w-4" />
            </SimpleButton>
          </SimpleCard>
        </section>
      </div>
    </main>
  );
}
