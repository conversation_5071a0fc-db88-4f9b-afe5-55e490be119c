'use client';

import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  GitBranch,
  Activity,
  Settings,
  BarChart3,
  FileText,
  Zap,
  ArrowRight,
  TrendingUp,
  Shield,
  Clock
} from 'lucide-react';

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-foreground">Loading...</div>
      </div>
    );
  }

  if (!session) {
    if (typeof window !== 'undefined') router.push('/auth/signin');
    return null;
  }

  return (
    <main className="p-6 space-y-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <header className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold text-foreground mb-2">
              Welcome back, {session.user?.name || 'Developer'}!
            </h1>
            <p className="text-muted-foreground text-lg">
              Manage your AI-powered code reviews and repository monitoring
            </p>
          </div>
          <Button
            onClick={() => signOut()}
            variant="destructive"
            className="shadow-lg"
          >
            Sign Out
          </Button>
        </header>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Active Repositories
              </CardTitle>
              <GitBranch className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">12</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-400">+2</span> from last month
              </p>
            </CardContent>
          </Card>

          <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Reviews This Week
              </CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">47</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-400">+12%</span> from last week
              </p>
            </CardContent>
          </Card>

          <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Issues Found
              </CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">23</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-red-400">+3</span> critical issues
              </p>
            </CardContent>
          </Card>

          <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Avg Review Time
              </CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">2.4m</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-400">-0.3m</span> faster than usual
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Feature Cards */}
        <section className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Manage Repositories */}
          <Card className="group hover:shadow-lg transition-all duration-300 border-border/50 bg-card/50 backdrop-blur-sm">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <GitBranch className="h-6 w-6 text-primary" />
                <CardTitle className="text-xl">Monitored Repositories</CardTitle>
              </div>
              <CardDescription>
                Select which GitHub repositories you want to monitor for AI-powered code reviews.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={() => router.push('/repositories')}
                className="w-full group-hover:bg-primary/90 transition-colors"
              >
                Manage Repositories
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardContent>
          </Card>

          {/* PR Activity */}
          <Card className="group hover:shadow-lg transition-all duration-300 border-border/50 bg-card/50 backdrop-blur-sm">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Activity className="h-6 w-6 text-purple-400" />
                <CardTitle className="text-xl">Pull Request Activity</CardTitle>
              </div>
              <CardDescription>
                View recent pull requests and AI review status for your monitored repositories.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={() => router.push('/dashboard/pr-activity')}
                variant="secondary"
                className="w-full group-hover:bg-secondary/90 transition-colors"
              >
                View PR Activity
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardContent>
          </Card>

          {/* Webhook Setup */}
          <Card className="group hover:shadow-lg transition-all duration-300 border-border/50 bg-card/50 backdrop-blur-sm">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Settings className="h-6 w-6 text-green-400" />
                <CardTitle className="text-xl">Webhook Setup</CardTitle>
              </div>
              <CardDescription>
                Configure automatic AI reviews for your repositories using GitHub webhooks.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={() => router.push('/dashboard/webhook-setup')}
                variant="outline"
                className="w-full group-hover:bg-accent transition-colors border-green-400/20 hover:border-green-400/40"
              >
                Setup Webhooks
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardContent>
          </Card>

          {/* Review Settings */}
          <Card className="group hover:shadow-lg transition-all duration-300 border-border/50 bg-card/50 backdrop-blur-sm">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Settings className="h-6 w-6 text-orange-400" />
                <CardTitle className="text-xl">Review Settings</CardTitle>
              </div>
              <CardDescription>
                Customize your AI review preferences and notification settings.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={() => router.push('/dashboard/settings')}
                variant="outline"
                className="w-full group-hover:bg-accent transition-colors border-orange-400/20 hover:border-orange-400/40"
              >
                Configure Settings
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardContent>
          </Card>

          {/* Analytics */}
          <Card className="group hover:shadow-lg transition-all duration-300 border-border/50 bg-card/50 backdrop-blur-sm">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <BarChart3 className="h-6 w-6 text-blue-400" />
                <CardTitle className="text-xl">Analytics & Insights</CardTitle>
              </div>
              <CardDescription>
                Track code quality improvements and review statistics over time.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                disabled
                variant="outline"
                className="w-full opacity-50 cursor-not-allowed"
              >
                Coming Soon
                <Clock className="ml-2 h-4 w-4" />
              </Button>
            </CardContent>
          </Card>

          {/* DeepAudit - Featured Card */}
          <Card className="group hover:shadow-xl transition-all duration-300 border-cyan-400/30 bg-gradient-to-br from-cyan-950/50 to-blue-950/50 backdrop-blur-sm relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-cyan-400/5 to-blue-400/5" />
            <CardHeader className="relative">
              <div className="flex items-center space-x-2">
                <Zap className="h-6 w-6 text-cyan-400" />
                <CardTitle className="text-xl text-cyan-100">DeepAudit</CardTitle>
                <span className="px-2 py-1 text-xs bg-cyan-400/20 text-cyan-300 rounded-full">
                  AI-Powered
                </span>
              </div>
              <CardDescription className="text-cyan-200/80">
                Run a comprehensive, step-by-step AI audit on any of your repositories. Get detailed reports on code quality, security, performance, and more.
              </CardDescription>
            </CardHeader>
            <CardContent className="relative">
              <Button
                onClick={() => router.push('/deepaudit')}
                className="w-full bg-cyan-600 hover:bg-cyan-500 text-white group-hover:shadow-lg transition-all"
              >
                Go to DeepAudit
                <Zap className="ml-2 h-4 w-4" />
              </Button>
            </CardContent>
          </Card>

          {/* Release Notes */}
          <Card className="group hover:shadow-lg transition-all duration-300 border-border/50 bg-card/50 backdrop-blur-sm">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <FileText className="h-6 w-6 text-indigo-400" />
                <CardTitle className="text-xl">AI Release Notes</CardTitle>
              </div>
              <CardDescription>
                Generate professional release notes automatically from your commits and PRs.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={() => router.push('/dashboard/release-notes')}
                variant="outline"
                className="w-full group-hover:bg-accent transition-colors border-indigo-400/20 hover:border-indigo-400/40"
              >
                Generate Notes
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardContent>
          </Card>
        </section>
      </div>
    </main>
  );
}