import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session || !session.accessToken || !session.user?.email) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Get user's monitored repositories
  const user = await prisma.user.findUnique({
    where: { email: session.user.email },
    include: { monitoredRepos: true },
  });
  if (!user || user.monitoredRepos.length === 0) {
    return NextResponse.json([]);
  }

  // Fetch PRs for each monitored repo
  const prs: any[] = [];
  for (const repo of user.monitoredRepos) {
    const res = await fetch(`https://api.github.com/repos/${repo.repoOwner}/${repo.repoName}/pulls?per_page=5`, {
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
        Accept: 'application/vnd.github+json',
      },
    });
    if (res.ok) {
      const repoPRs = await res.json();
      prs.push(...repoPRs.map((pr: any) => ({
        id: pr.id,
        number: pr.number,
        title: pr.title,
        state: pr.state,
        html_url: pr.html_url,
        repo: repo.repoName,
        owner: repo.repoOwner,
        user: pr.user?.login,
        created_at: pr.created_at,
        updated_at: pr.updated_at,
      })));
    }
  }

  return NextResponse.json(prs);
} 