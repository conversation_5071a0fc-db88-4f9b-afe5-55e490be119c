'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { signIn } from 'next-auth/react';
import ReactMarkdown from 'react-markdown';
import CodeQualityScore from '@/app/components/CodeQualityScore';
import jsPDF from 'jspdf';

export default function PRReviewPage() {
  const params = useParams();
  const { owner, repo, number } = params as { owner: string; repo: string; number: string };
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [diff, setDiff] = useState('');
  const [prTitle, setPrTitle] = useState('');
  const [prDescription, setPrDescription] = useState('');
  const [review, setReview] = useState('');
  const [reviewLoading, setReviewLoading] = useState(false);
  const [postStatus, setPostStatus] = useState<'idle' | 'posting' | 'success' | 'error' | 'rate-limit' | 'reauth'>('idle');
  const [postError, setPostError] = useState<string | null>(null);
  const [editableReview, setEditableReview] = useState('');
  const [reauth, setReauth] = useState(false);
  const [repositoryId, setRepositoryId] = useState<string | null>(null);
  const [reviewId, setReviewId] = useState<string | null>(null);
  const [reformatPrompt, setReformatPrompt] = useState('');
  const [reformatLoading, setReformatLoading] = useState(false);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [reformatError, setReformatError] = useState<string | null>(null);
  const [updateSuccess, setUpdateSuccess] = useState(false);
  const [codeQualityReview, setCodeQualityReview] = useState<any[]>([]);
  const [cqLoading, setCqLoading] = useState(false);
  const [cqError, setCqError] = useState<string | null>(null);
  const [postCQStatus, setPostCQStatus] = useState<'idle' | 'posting' | 'success' | 'error'>('idle');
  const [copyCQStatus, setCopyCQStatus] = useState<'idle' | 'success'>('idle');
  const [postCQError, setPostCQError] = useState<string | null>(null);
  const [shareableLink, setShareableLink] = useState<string | null>(null);
  const [shareStatus, setShareStatus] = useState<'idle' | 'generating' | 'success'>('idle');
  const [qaQuestion, setQaQuestion] = useState('');
  const [qaAnswer, setQaAnswer] = useState<string | null>(null);
  const [qaLoading, setQaLoading] = useState(false);
  const [qaError, setQaError] = useState<string | null>(null);

  // Fetch monitored repositoryId
  useEffect(() => {
    async function fetchRepositoryId() {
      try {
        const res = await fetch('/api/monitored-repos');
        if (!res.ok) return;
        const repos = await res.json();
        const match = repos.find((r: any) => r.repoOwner === owner && r.repoName === repo);
        if (match) setRepositoryId(match.id);
      } catch {}
    }
    fetchRepositoryId();
  }, [owner, repo]);

  // Fetch PR details and diff
  useEffect(() => {
    async function fetchPR() {
      setLoading(true);
      setError(null);
      try {
        const res = await fetch(`/api/github/pr-diff?owner=${owner}&repo=${repo}&number=${number}`);
        if (!res.ok) {
          const data = await res.json();
          if (res.status === 401 && data.reauth) {
            setReauth(true);
            setError('Your GitHub session has expired. Please re-authenticate.');
          } else {
            setError(data.error || 'Failed to fetch PR diff');
          }
          setLoading(false);
          return;
        }
        const data = await res.json();
        setDiff(data.diff);
        setPrTitle(data.title);
        setPrDescription(data.body);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    fetchPR();
  }, [owner, repo, number]);

  // Fetch or generate persistent AI review
  useEffect(() => {
    if (!repositoryId || !number) return;
    async function fetchOrGenerateReview() {
      setReviewLoading(true);
      setError(null);
      try {
        // Try to fetch existing review
        const res = await fetch(`/api/review?repositoryId=${repositoryId}&pullRequestId=${number}`);
        if (res.ok) {
          const data = await res.json();
          if (data.review) {
            setReview(data.review);
            setEditableReview(data.review);
            setReviewId(data.id);
            setReviewLoading(false);
            return;
          }
        }
        // If not found, generate and store
        const aiRes = await fetch('/api/ai/review-pr', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ prTitle, prDescription, diff }),
        });
        const aiData = await aiRes.json();
        if (aiData.review) {
          setReview(aiData.review);
          setEditableReview(aiData.review);
          // Store in DB
          const storeRes = await fetch('/api/review', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              pullRequestId: number?.toString(),
              repositoryId,
              review: aiData.review,
            }),
          });
          if (storeRes.ok) {
            const storeData = await storeRes.json();
            setReviewId(storeData.id);
          }
        } else if (aiData.error) {
          setError(aiData.error);
        }
      } catch (err: any) {
        setError(err.message);
      }
      setReviewLoading(false);
    }
    fetchOrGenerateReview();
  }, [repositoryId, number, prTitle, prDescription, diff]);

  // Fetch code quality review
  useEffect(() => {
    async function fetchCQReview() {
      if (!repositoryId || !number || !diff) return;
      setCqLoading(true);
      setCqError(null);
      try {
        // Try to fetch from DB first
        const getRes = await fetch(`/api/ai/code-quality-review?repositoryId=${repositoryId}&pullRequestId=${number}`);
        const getData = await getRes.json();
        if (getRes.ok && getData.review) {
          setCodeQualityReview(getData.review);
          setCqLoading(false);
          return;
        }
        // If not found, generate and store
        const res = await fetch('/api/ai/code-quality-review', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            diff,
            prTitle,
            prDescription,
            repositoryId,
            pullRequestId: number?.toString(),
          }),
        });
        const data = await res.json();
        if (data.review) setCodeQualityReview(data.review);
        else setCqError(data.error || 'No review found');
      } catch (err: any) {
        setCqError(err.message);
      }
      setCqLoading(false);
    }
    fetchCQReview();
  }, [diff, repositoryId, number]);

  // Post review as a comment on GitHub
  const postReview = async () => {
    setPostStatus('posting');
    setPostError(null);
    try {
      const res = await fetch('/api/github/post-review', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ owner, repo, number, body: editableReview }),
      });
      const data = await res.json();
      if (data.type === 'inline') {
        setPostStatus('success');
      } else if (data.type === 'summary') {
        setPostStatus('success');
      } else if (res.status === 429) {
        setPostStatus('rate-limit');
      } else if (res.status === 401) {
        setPostStatus('reauth');
      } else if (!res.ok) {
        setPostStatus('error');
        setPostError(data.error || 'Failed to post review');
      } else {
        setPostStatus('success');
      }
    } catch (err: any) {
      setPostError(err.message);
      setPostStatus('error');
    }
  };

  // Handler for AI reformat
  const handleReformat = async () => {
    setReformatLoading(true);
    setReformatError(null);
    setUpdateSuccess(false);
    try {
      const res = await fetch('/api/ai/reformat-review', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ review: editableReview, prompt: reformatPrompt }),
      });
      const data = await res.json();
      if (data.reformatted) {
        setEditableReview(data.reformatted);
      } else if (data.error) {
        setReformatError(data.error);
      }
    } catch (err: any) {
      setReformatError(err.message);
    }
    setReformatLoading(false);
  };

  // Handler for updating review in DB
  const handleUpdateReview = async () => {
    if (!repositoryId || !number) return;
    setUpdateLoading(true);
    setUpdateSuccess(false);
    setReformatError(null);
    try {
      const res = await fetch('/api/review', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          pullRequestId: number?.toString(),
          repositoryId,
          review: editableReview,
        }),
      });
      if (res.ok) setUpdateSuccess(true);
      else {
        const data = await res.json();
        setReformatError(data.error || 'Failed to update review');
      }
    } catch (err: any) {
      setReformatError(err.message);
    }
    setUpdateLoading(false);
  };

  // Format code quality review as Markdown
  function formatCQReviewMarkdown(items: any[]): string {
    return [
      '### 🛡️ Code Quality AI Review',
      ...items.map(item => {
        let icon = '';
        if (item.type === 'security') icon = '🚨';
        else if (item.type === 'performance') icon = '⚡';
        else if (item.type === 'quality' && item.level === 'success') icon = '✨';
        else if (item.type === 'tip') icon = '💡';
        let md = `\n**${icon} ${item.title}**\n\n${item.message}`;
        if (item.code) md += `\n\n
${item.code}
`;
        if (item.line) md += `\n_Line: ${item.line}_`;
        return md;
      })
    ].join('\n\n');
  }

  // Handler: Post Code Quality Review to GitHub
  const handlePostCQToGitHub = async () => {
    setPostCQStatus('posting');
    setPostCQError(null);
    try {
      const markdown = formatCQReviewMarkdown(codeQualityReview);
      const res = await fetch('/api/github/post-review', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          owner: params.owner,
          repo: params.repo,
          number: params.number,
          body: markdown,
        }),
      });
      if (res.ok) setPostCQStatus('success');
      else {
        setPostCQStatus('error');
        const data = await res.json();
        setPostCQError(data.error || 'Failed to post review');
      }
    } catch (err: any) {
      setPostCQStatus('error');
      setPostCQError(err.message);
    }
  };

  // Handler: Copy Code Quality Review to clipboard
  const handleCopyCQReview = async () => {
    try {
      const markdown = formatCQReviewMarkdown(codeQualityReview);
      await navigator.clipboard.writeText(markdown);
      setCopyCQStatus('success');
      setTimeout(() => setCopyCQStatus('idle'), 1500);
    } catch {}
  };

  // Handler: Export as Markdown
  const handleExportMarkdown = () => {
    const markdown = formatCQReviewMarkdown(codeQualityReview);
    const blob = new Blob([markdown], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `code-quality-review-${params.owner}-${params.repo}-pr${params.number}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Handler: Export as PDF
  const handleExportPDF = () => {
    const doc = new jsPDF();
    let y = 12;
    // Header
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(18);
    doc.text('Amazingly AI Code Reviewer', 10, y);
    y += 10;
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text(`Repository: ${params.owner}/${params.repo}`, 10, y);
    y += 7;
    doc.text(`Pull Request: #${params.number}`, 10, y);
    y += 7;
    if (prTitle) {
      doc.setFont('helvetica', 'italic');
      doc.text(`Title: ${prTitle}`, 10, y);
      doc.setFont('helvetica', 'normal');
      y += 8;
    }
    // Horizontal line
    doc.setDrawColor(180);
    doc.setLineWidth(0.5);
    doc.line(10, y, 200, y);
    y += 6;
    doc.setFontSize(16);
    doc.setFont('helvetica', 'bold');
    doc.text('Code Quality AI Review', 10, y);
    y += 10;
    doc.setFontSize(12);
    codeQualityReview.forEach((item, idx) => {
      if (y > 250) { doc.addPage(); y = 12; }
      let icon = '';
      let color: [number, number, number] = [0,0,0];
      if (item.type === 'security') { icon = '🚨'; color = [200,0,0]; }
      else if (item.type === 'performance') { icon = '⚡'; color = [200,150,0]; }
      else if (item.type === 'quality' && item.level === 'success') { icon = '✨'; color = [0,150,0]; }
      else if (item.type === 'tip') { icon = '💡'; color = [0,80,200]; }
      // Section box
      doc.setDrawColor(color[0], color[1], color[2]);
      doc.setLineWidth(1);
      doc.roundedRect(8, y-3, 194, 22, 2, 2, 'S');
      // Section header
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(color[0], color[1], color[2]);
      doc.text(`${icon} ${item.title}`, 12, y+7);
      doc.setFont('helvetica', 'normal');
      doc.setTextColor(0,0,0);
      y += 13;
      // Message
      const messageLines = doc.splitTextToSize(item.message, 180);
      doc.text(messageLines, 12, y);
      y += messageLines.length * 6 + 2;
      // Code block
      if (item.code) {
        doc.setFont('courier', 'normal');
        doc.setFontSize(10);
        doc.setFillColor(240,240,240);
        doc.rect(12, y-2, 180, 7 * (item.code.split('\n').length), 'F');
        doc.setTextColor(30,120,30);
        const codeLines = doc.splitTextToSize(item.code, 176);
        doc.text(codeLines, 14, y+5);
        y += codeLines.length * 7 + 2;
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(12);
        doc.setTextColor(0,0,0);
      }
      // Line info
      if (item.line) {
        doc.setTextColor(100);
        doc.text(`Line: ${item.line}`, 12, y);
        doc.setTextColor(0,0,0);
        y += 6;
      }
      // Section separator
      y += 6;
      if (idx < codeQualityReview.length - 1) {
        doc.setDrawColor(220);
        doc.setLineWidth(0.5);
        doc.line(10, y, 200, y);
        y += 4;
      }
    });
    // Footer
    const dateStr = new Date().toLocaleString();
    doc.setFontSize(9);
    doc.setTextColor(120);
    doc.text(`Generated by Amazingly AI Code Reviewer on ${dateStr}`, 10, 290);
    doc.save(`code-quality-review-${params.owner}-${params.repo}-pr${params.number}.pdf`);
  };

  // Handler: Generate Shareable Link
  const handleShareableLink = async () => {
    setShareStatus('generating');
    setShareableLink(null);
    try {
      // Call backend to get a shareable link (could be /api/share/code-quality-review/[id])
      const res = await fetch(`/api/share/code-quality-review?repositoryId=${repositoryId}&pullRequestId=${number}`);
      const data = await res.json();
      if (data.url) {
        setShareableLink(data.url);
        setShareStatus('success');
      } else {
        setShareStatus('idle');
      }
    } catch {
      setShareStatus('idle');
    }
  };

  const handleAskAI = async () => {
    setQaLoading(true);
    setQaError(null);
    setQaAnswer(null);
    try {
      const res = await fetch('/api/ai/qa', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          diff,
          prTitle,
          prDescription,
          question: qaQuestion,
        }),
      });
      const data = await res.json();
      if (res.ok && data.answer) {
        setQaAnswer(data.answer);
      } else {
        setQaError(data.error || 'Failed to get answer');
      }
    } catch (err: any) {
      setQaError(err.message);
    }
    setQaLoading(false);
  };

  return (
    <main
      className="p-6"
      style={{backgroundColor: 'var(--background)', minHeight: '100vh'}}
    >
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <header className="mb-8">
          <h1 className="text-3xl font-bold mb-2" style={{color: 'var(--foreground)'}}>
            Review PR: {owner}/{repo} #{number}
          </h1>
          <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
            AI-powered code review and quality analysis
          </p>
        </header>

        {/* Code Quality Score Component */}
        {repositoryId && (
          <div className="mb-8">
            <CodeQualityScore repositoryId={repositoryId} pullRequestId={number?.toString()} />
          </div>
        )}
      {loading ? (
        <div>Loading PR diff...</div>
      ) : error ? (
        <div className="text-red-600">
          {error}
          {reauth && (
            <button
              onClick={() => signIn('github')}
              className="ml-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Re-authenticate with GitHub
            </button>
          )}
        </div>
      ) : (
        <>
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h3 className="text-xl font-semibold mb-2">{prTitle}</h3>
            <div className="prose prose-blue max-w-none mb-2">
              <ReactMarkdown>{prDescription}</ReactMarkdown>
            </div>
            <details className="mb-2">
              <summary className="cursor-pointer font-semibold text-blue-700">Show PR Diff</summary>
              <pre className="bg-gray-900 text-green-200 rounded p-4 overflow-x-auto text-xs mt-2">{diff}</pre>
            </details>
          </div>
          <div className="bg-gray-900 text-white rounded-lg shadow p-6 whitespace-pre-wrap overflow-x-auto mb-4">
            <h3 className="text-xl font-semibold mb-2 text-blue-300">AI Review Result (Editable)</h3>
            {reviewLoading ? (
              <div className="text-blue-400">Running AI review...</div>
            ) : error ? (
              <div className="text-red-400">{error}</div>
            ) : review ? (
              <>
                <textarea
                  value={editableReview}
                  onChange={e => setEditableReview(e.target.value)}
                  className="w-full h-64 p-2 rounded bg-gray-800 text-white font-mono text-sm border border-gray-700 mb-2"
                />
                <div className="flex flex-col md:flex-row gap-2 mb-2">
                  <input
                    type="text"
                    value={reformatPrompt}
                    onChange={e => setReformatPrompt(e.target.value)}
                    placeholder="E.g. Make it shorter, use bullet points, etc."
                    className="flex-1 px-3 py-2 rounded border border-gray-400 text-sm bg-white text-black"
                    disabled={reformatLoading}
                  />
                  <button
                    onClick={handleReformat}
                    className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 font-semibold disabled:opacity-50"
                    disabled={reformatLoading || !reformatPrompt}
                  >
                    {reformatLoading ? 'Reformatting...' : 'Reformat with AI'}
                  </button>
                  <button
                    onClick={handleUpdateReview}
                    className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 font-semibold disabled:opacity-50"
                    disabled={updateLoading}
                  >
                    {updateLoading ? 'Updating...' : 'Update Review'}
                  </button>
                </div>
                {reformatError && <div className="text-red-500 mb-2">{reformatError}</div>}
                {updateSuccess && <div className="text-green-600 mb-2">Review updated!</div>}
              </>
            ) : (
              <div className="text-gray-400">No review result yet.</div>
            )}
          </div>
          <button
            onClick={postReview}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 font-semibold disabled:opacity-50"
            disabled={!editableReview || postStatus === 'posting'}
          >
            {postStatus === 'posting' ? 'Posting...' : 'Post Review as GitHub Comment'}
          </button>
          {postStatus === 'success' && <div className="text-green-600 mt-2">Review posted to GitHub!</div>}
          {postStatus === 'error' && <div className="text-red-600 mt-2">{postError}</div>}
          {postStatus === 'rate-limit' && <div className="text-yellow-600 mt-2">GitHub API rate limit exceeded. Please try again later.</div>}
          {postStatus === 'reauth' && <div className="text-yellow-600 mt-2">Your GitHub session has expired. Please re-authenticate.</div>}
          {cqLoading ? (
            <div className="my-6">Loading Code Quality Review...</div>
          ) : cqError ? (
            <div className="my-6 text-red-500">{cqError}</div>
          ) : codeQualityReview.length > 0 ? (
            <div className="my-6">
              <h3 className="text-2xl font-bold mb-4">Code Quality AI Review</h3>
              <div className="space-y-4">
                {codeQualityReview.map((item, idx) => (
                  <div
                    key={idx}
                    className={`rounded-lg p-4 shadow flex flex-col gap-2 ${
                      item.type === 'security'
                        ? 'bg-red-100 border-l-8 border-red-500'
                        : item.type === 'performance'
                        ? 'bg-yellow-100 border-l-8 border-yellow-500'
                        : item.type === 'quality' && item.level === 'success'
                        ? 'bg-green-100 border-l-8 border-green-500'
                        : 'bg-blue-100 border-l-8 border-blue-500'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      {item.type === 'security' && <span className="text-red-600 text-xl">🚨</span>}
                      {item.type === 'performance' && <span className="text-yellow-600 text-xl">⚡</span>}
                      {item.type === 'quality' && item.level === 'success' && <span className="text-green-600 text-xl">✨</span>}
                      {item.type === 'tip' && <span className="text-blue-600 text-xl">💡</span>}
                      <span className="font-bold text-lg text-gray-900">{item.title}</span>
                    </div>
                    <div className="text-gray-800 whitespace-pre-line">{item.message}</div>
                    {item.code && (
                      <pre className="bg-gray-900 text-green-200 rounded p-2 overflow-x-auto text-xs mt-2">{item.code}</pre>
                    )}
                    {item.line && (
                      <div className="text-xs text-gray-500">Line: {item.line}</div>
                    )}
                  </div>
                ))}
              </div>
              <div className="flex gap-2 mt-4">
                <button
                  onClick={handlePostCQToGitHub}
                  className="bg-blue-700 text-white px-4 py-2 rounded hover:bg-blue-800 font-semibold disabled:opacity-50"
                  disabled={postCQStatus === 'posting'}
                >
                  {postCQStatus === 'posting' ? 'Posting...' : 'Post to GitHub'}
                </button>
                <button
                  onClick={handleCopyCQReview}
                  className="bg-gray-700 text-white px-4 py-2 rounded hover:bg-gray-800 font-semibold"
                >
                  {copyCQStatus === 'success' ? 'Copied!' : 'Copy Review'}
                </button>
                {postCQStatus === 'success' && <span className="text-green-600 ml-2">Posted!</span>}
                {postCQStatus === 'error' && <span className="text-red-600 ml-2">{postCQError}</span>}
              </div>
              <div className="flex gap-2 mt-2 flex-wrap">
                <button
                  onClick={handleExportMarkdown}
                  className="bg-purple-700 text-white px-4 py-2 rounded hover:bg-purple-800 font-semibold"
                >
                  Export as Markdown
                </button>
                <button
                  onClick={handleExportPDF}
                  className="bg-pink-700 text-white px-4 py-2 rounded hover:bg-pink-800 font-semibold"
                >
                  Export as PDF
                </button>
                <button
                  onClick={handleShareableLink}
                  className="bg-teal-700 text-white px-4 py-2 rounded hover:bg-teal-800 font-semibold"
                  disabled={shareStatus === 'generating'}
                >
                  {shareStatus === 'generating' ? 'Generating...' : 'Shareable Link'}
                </button>
                {shareableLink && (
                  <span className="ml-2">
                    <input
                      type="text"
                      value={shareableLink}
                      readOnly
                      className="px-2 py-1 rounded border border-gray-400 text-xs w-64 bg-white text-black"
                      onFocus={e => e.target.select()}
                    />
                    <button
                      onClick={() => { navigator.clipboard.writeText(shareableLink); }}
                      className="ml-1 px-2 py-1 bg-gray-600 text-white rounded text-xs hover:bg-gray-700"
                    >Copy Link</button>
                  </span>
                )}
              </div>
            </div>
          ) : null}
          {codeQualityReview.length > 0 && (
            <div className="my-8">
              <h3 className="text-2xl font-bold mb-4">Ask AI about this Pull Request</h3>
              <div className="bg-white rounded-lg shadow p-6 flex flex-col gap-4">
                <textarea
                  value={qaQuestion}
                  onChange={e => setQaQuestion(e.target.value)}
                  placeholder="Ask a question about this PR, code changes, or review..."
                  className="w-full min-h-20 p-2 rounded border border-gray-400 text-sm bg-gray-50 text-black"
                  disabled={qaLoading}
                />
                <button
                  onClick={handleAskAI}
                  className="self-start bg-blue-700 text-white px-4 py-2 rounded hover:bg-blue-800 font-semibold disabled:opacity-50"
                  disabled={qaLoading || !qaQuestion.trim()}
                >
                  {qaLoading ? 'Thinking...' : 'Ask AI'}
                </button>
                {qaError && <div className="text-red-600">{qaError}</div>}
                {qaAnswer && (
                  <div className="bg-gray-100 rounded p-4 text-gray-900 whitespace-pre-line mt-2">
                    <strong>AI Answer:</strong>
                    <div className="mt-1">{qaAnswer}</div>
                  </div>
                )}
              </div>
            </div>
          )}
        </>
      )}
      </div>
    </main>
  );
}