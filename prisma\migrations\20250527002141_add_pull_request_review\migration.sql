-- CreateTable
CREATE TABLE "PullRequestReview" (
    "id" TEXT NOT NULL,
    "pullRequestId" TEXT NOT NULL,
    "repositoryId" TEXT NOT NULL,
    "review" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PullRequestReview_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "PullRequestReview_repositoryId_idx" ON "PullRequestReview"("repositoryId");

-- CreateIndex
CREATE UNIQUE INDEX "PullRequestReview_pullRequestId_repositoryId_key" ON "PullRequestReview"("pullRequestId", "repositoryId");
