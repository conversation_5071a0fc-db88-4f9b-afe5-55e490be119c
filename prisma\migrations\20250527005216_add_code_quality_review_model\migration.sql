-- CreateTable
CREATE TABLE "CodeQualityReview" (
    "id" TEXT NOT NULL,
    "pullRequestId" TEXT NOT NULL,
    "repositoryId" TEXT NOT NULL,
    "review" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CodeQualityReview_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "CodeQualityReview_repositoryId_idx" ON "CodeQualityReview"("repositoryId");

-- CreateIndex
CREATE UNIQUE INDEX "CodeQualityReview_pullRequestId_repositoryId_key" ON "CodeQualityReview"("pullRequestId", "repositoryId");
