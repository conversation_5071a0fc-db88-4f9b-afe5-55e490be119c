'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  Webhook,
  Copy,
  Check,
  ExternalLink,
  Settings,
  GitBranch,
  Shield,
  Zap,
  AlertTriangle,
  Info,
  CheckCircle,
  ArrowRight
} from 'lucide-react';

// Create a client-only component
const WebhookSetupContent = () => {
  const [copiedUrl, setCopiedUrl] = useState(false);
  const [copiedSecret, setCopiedSecret] = useState(false);
  const [webhookUrl, setWebhookUrl] = useState('');
  const [activeStep, setActiveStep] = useState(1);
  const webhookSecret = process.env.NEXT_PUBLIC_GITHUB_WEBHOOK_SECRET || 'your-webhook-secret';

  useEffect(() => {
    setWebhookUrl(`${window.location.origin}/api/webhooks/github`);
  }, []);

  const copyToClipboard = (text: string, type: 'url' | 'secret') => {
    navigator.clipboard.writeText(text);
    if (type === 'url') {
      setCopiedUrl(true);
      setTimeout(() => setCopiedUrl(false), 2000);
    } else {
      setCopiedSecret(true);
      setTimeout(() => setCopiedSecret(false), 2000);
    }
  };

  const steps = [
    {
      id: 1,
      title: 'Navigate to Repository Settings',
      description: 'Go to your GitHub repository or organization settings',
      icon: Settings
    },
    {
      id: 2,
      title: 'Add Webhook',
      description: 'Navigate to Webhooks section and click "Add webhook"',
      icon: Webhook
    },
    {
      id: 3,
      title: 'Configure Webhook',
      description: 'Enter the webhook URL and secret provided below',
      icon: Shield
    },
    {
      id: 4,
      title: 'Select Events',
      description: 'Choose "Pull requests" as the trigger event',
      icon: GitBranch
    },
    {
      id: 5,
      title: 'Activate',
      description: 'Save the webhook to enable automatic AI reviews',
      icon: Zap
    }
  ];

  return (
    <main
      className="p-6"
      style={{backgroundColor: 'var(--background)', minHeight: '100vh'}}
    >
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <header className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-3">
            <Webhook className="h-8 w-8" style={{color: 'var(--primary)'}} />
            <div>
              <h1 className="text-3xl font-bold" style={{color: 'var(--foreground)'}}>
                GitHub Webhook Setup
              </h1>
              <p className="text-sm mt-1" style={{color: 'var(--muted-foreground)'}}>
                Configure automatic AI reviews for your repositories
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <a
              href="https://docs.github.com/en/developers/webhooks-and-events/webhooks"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors hover:opacity-80"
              style={{
                backgroundColor: 'var(--secondary)',
                color: 'var(--secondary-foreground)'
              }}
            >
              <ExternalLink className="h-4 w-4" />
              <span>GitHub Docs</span>
            </a>
          </div>
        </header>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div
            className="rounded-lg p-6"
            style={{
              backgroundColor: 'var(--card)',
              border: '1px solid var(--border)'
            }}
          >
            <div className="flex items-center space-x-3 mb-3">
              <Zap className="h-6 w-6 text-yellow-400" />
              <h3 className="text-lg font-semibold" style={{color: 'var(--foreground)'}}>
                Automatic Reviews
              </h3>
            </div>
            <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
              AI reviews are triggered automatically when pull requests are created or updated
            </p>
          </div>

          <div
            className="rounded-lg p-6"
            style={{
              backgroundColor: 'var(--card)',
              border: '1px solid var(--border)'
            }}
          >
            <div className="flex items-center space-x-3 mb-3">
              <Shield className="h-6 w-6 text-blue-400" />
              <h3 className="text-lg font-semibold" style={{color: 'var(--foreground)'}}>
                Secure Connection
              </h3>
            </div>
            <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
              Webhooks use secret validation to ensure secure communication with GitHub
            </p>
          </div>

          <div
            className="rounded-lg p-6"
            style={{
              backgroundColor: 'var(--card)',
              border: '1px solid var(--border)'
            }}
          >
            <div className="flex items-center space-x-3 mb-3">
              <GitBranch className="h-6 w-6 text-green-400" />
              <h3 className="text-lg font-semibold" style={{color: 'var(--foreground)'}}>
                Repository Level
              </h3>
            </div>
            <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
              Set up webhooks at repository or organization level for maximum flexibility
            </p>
          </div>
        </div>

        {/* Step-by-Step Guide */}
        <section
          className="rounded-lg p-6 mb-8"
          style={{
            backgroundColor: 'var(--card)',
            border: '1px solid var(--border)'
          }}
        >
          <div className="flex items-center space-x-2 mb-6">
            <Settings className="h-5 w-5" style={{color: 'var(--primary)'}} />
            <h2 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
              Setup Instructions
            </h2>
          </div>

          <div className="space-y-6">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = activeStep === step.id;
              const isCompleted = activeStep > step.id;

              return (
                <div
                  key={step.id}
                  className={`flex items-start space-x-4 p-4 rounded-lg border transition-all cursor-pointer ${
                    isActive ? 'ring-2 ring-opacity-50' : ''
                  }`}
                  style={{
                    backgroundColor: isActive ? 'var(--accent)' : 'var(--background)',
                    borderColor: isActive ? 'var(--primary)' : 'var(--border)'
                  }}
                  onClick={() => setActiveStep(step.id)}
                >
                  <div
                    className="flex items-center justify-center w-10 h-10 rounded-full border-2"
                    style={{
                      backgroundColor: isCompleted ? 'var(--primary)' : isActive ? 'var(--primary)' : 'transparent',
                      borderColor: isCompleted || isActive ? 'var(--primary)' : 'var(--border)',
                      color: isCompleted || isActive ? 'var(--primary-foreground)' : 'var(--muted-foreground)'
                    }}
                  >
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold mb-1" style={{color: 'var(--foreground)'}}>
                      {step.title}
                    </h3>
                    <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                      {step.description}
                    </p>
                  </div>
                  <div className="flex items-center">
                    <span className="text-xs font-medium px-2 py-1 rounded" style={{
                      backgroundColor: 'var(--secondary)',
                      color: 'var(--secondary-foreground)'
                    }}>
                      Step {step.id}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </section>

        {/* Configuration Section */}
        <section
          className="rounded-lg p-6 mb-8"
          style={{
            backgroundColor: 'var(--card)',
            border: '1px solid var(--border)'
          }}
        >
          <div className="flex items-center space-x-2 mb-6">
            <Shield className="h-5 w-5" style={{color: 'var(--primary)'}} />
            <h2 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
              Webhook Configuration
            </h2>
          </div>

          <div className="space-y-6">
            {/* Webhook URL */}
            <div>
              <label className="block text-sm font-medium mb-2" style={{color: 'var(--foreground)'}}>
                Webhook URL
              </label>
              <div className="flex items-center space-x-3">
                <div
                  className="flex-1 p-3 rounded-lg border font-mono text-sm"
                  style={{
                    backgroundColor: 'var(--background)',
                    borderColor: 'var(--border)',
                    color: 'var(--foreground)'
                  }}
                >
                  {webhookUrl || 'Loading...'}
                </div>
                <button
                  onClick={() => copyToClipboard(webhookUrl, 'url')}
                  className="flex items-center space-x-2 px-4 py-3 rounded-lg font-medium transition-colors hover:opacity-90"
                  style={{
                    backgroundColor: 'var(--primary)',
                    color: 'var(--primary-foreground)'
                  }}
                >
                  {copiedUrl ? (
                    <>
                      <Check className="h-4 w-4" />
                      <span>Copied!</span>
                    </>
                  ) : (
                    <>
                      <Copy className="h-4 w-4" />
                      <span>Copy</span>
                    </>
                  )}
                </button>
              </div>
              <p className="text-xs mt-2" style={{color: 'var(--muted-foreground)'}}>
                This URL will receive webhook events from GitHub
              </p>
            </div>

            {/* Webhook Secret */}
            <div>
              <label className="block text-sm font-medium mb-2" style={{color: 'var(--foreground)'}}>
                Webhook Secret
              </label>
              <div className="flex items-center space-x-3">
                <div
                  className="flex-1 p-3 rounded-lg border font-mono text-sm"
                  style={{
                    backgroundColor: 'var(--background)',
                    borderColor: 'var(--border)',
                    color: 'var(--foreground)'
                  }}
                >
                  {webhookSecret}
                </div>
                <button
                  onClick={() => copyToClipboard(webhookSecret, 'secret')}
                  className="flex items-center space-x-2 px-4 py-3 rounded-lg font-medium transition-colors hover:opacity-90"
                  style={{
                    backgroundColor: 'var(--primary)',
                    color: 'var(--primary-foreground)'
                  }}
                >
                  {copiedSecret ? (
                    <>
                      <Check className="h-4 w-4" />
                      <span>Copied!</span>
                    </>
                  ) : (
                    <>
                      <Copy className="h-4 w-4" />
                      <span>Copy</span>
                    </>
                  )}
                </button>
              </div>
              <p className="text-xs mt-2" style={{color: 'var(--muted-foreground)'}}>
                This secret validates that webhook requests come from GitHub
              </p>
            </div>

            {/* Additional Settings */}
            <div
              className="rounded-lg p-4"
              style={{
                backgroundColor: 'var(--accent)',
                border: '1px solid var(--border)'
              }}
            >
              <div className="flex items-start space-x-3">
                <Info className="h-5 w-5 mt-0.5" style={{color: 'var(--primary)'}} />
                <div>
                  <h4 className="font-medium mb-2" style={{color: 'var(--foreground)'}}>
                    Additional Configuration
                  </h4>
                  <ul className="space-y-1 text-sm" style={{color: 'var(--muted-foreground)'}}>
                    <li>• Content type: <code className="px-1 py-0.5 rounded text-xs" style={{backgroundColor: 'var(--background)'}}>application/json</code></li>
                    <li>• Events: <code className="px-1 py-0.5 rounded text-xs" style={{backgroundColor: 'var(--background)'}}>Pull requests</code></li>
                    <li>• Active: <code className="px-1 py-0.5 rounded text-xs" style={{backgroundColor: 'var(--background)'}}>Yes</code></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Warning Section */}
        <section
          className="rounded-lg p-6"
          style={{
            backgroundColor: 'var(--card)',
            border: '1px solid var(--border)'
          }}
        >
          <div className="flex items-start space-x-3">
            <AlertTriangle className="h-6 w-6 text-yellow-400 mt-0.5" />
            <div>
              <h3 className="font-semibold mb-2" style={{color: 'var(--foreground)'}}>
                Important Notes
              </h3>
              <ul className="space-y-2 text-sm" style={{color: 'var(--muted-foreground)'}}>
                <li>• Webhooks will only trigger for repositories you have monitoring enabled</li>
                <li>• Make sure your webhook secret matches the one configured in your environment</li>
                <li>• Test your webhook after setup to ensure it's working correctly</li>
                <li>• You can set up webhooks at the repository or organization level</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </main>
  );
};

// Export the page with dynamic import and no SSR
export default dynamic(() => Promise.resolve(WebhookSetupContent), {
  ssr: false,
}); 