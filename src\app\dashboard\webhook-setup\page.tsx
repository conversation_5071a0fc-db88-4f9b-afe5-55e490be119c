'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Create a client-only component
const WebhookSetupContent = () => {
  const [copied, setCopied] = useState(false);
  const [webhookUrl, setWebhookUrl] = useState('');
  const webhookSecret = process.env.NEXT_PUBLIC_GITHUB_WEBHOOK_SECRET || 'your-webhook-secret';

  useEffect(() => {
    setWebhookUrl(`${window.location.origin}/api/webhooks/github`);
  }, []);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h2 className="text-3xl font-bold mb-4 text-white">GitHub Webhook Setup</h2>
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h3 className="text-xl font-semibold mb-2">Instructions</h3>
        <p className="mb-4">
          To enable hands-off AI reviews, add a webhook to your GitHub repository or organization:
        </p>
        <ol className="list-decimal pl-5 mb-4">
          <li>Go to your repository or organization settings.</li>
          <li>Navigate to Webhooks &gt; Add webhook.</li>
          <li>Enter the following URL:</li>
          <div className="flex items-center mt-2">
            <code className="bg-gray-100 p-2 rounded flex-grow">{webhookUrl}</code>
            <button
              onClick={() => copyToClipboard(webhookUrl)}
              className="ml-2 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              {copied ? 'Copied!' : 'Copy'}
            </button>
          </div>
          <li className="mt-2">Set the content type to <code>application/json</code>.</li>
          <li>Enter the following secret:</li>
          <div className="flex items-center mt-2">
            <code className="bg-gray-100 p-2 rounded flex-grow">{webhookSecret}</code>
            <button
              onClick={() => copyToClipboard(webhookSecret)}
              className="ml-2 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              {copied ? 'Copied!' : 'Copy'}
            </button>
          </div>
          <li className="mt-2">Select the events: <code>Pull request</code>.</li>
          <li>Click &quot;Add webhook&quot;.</li>
        </ol>
        <p className="text-gray-600">
          Once set up, the webhook will automatically trigger AI reviews for new or updated pull requests.
        </p>
      </div>
    </div>
  );
};

// Export the page with dynamic import and no SSR
export default dynamic(() => Promise.resolve(WebhookSetupContent), {
  ssr: false,
}); 