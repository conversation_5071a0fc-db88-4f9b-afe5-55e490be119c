-- CreateTable
CREATE TABLE "DeepAudit" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "repositoryId" TEXT NOT NULL,
    "repoName" TEXT NOT NULL,
    "repoOwner" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "startedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "progress" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "report" JSONB,
    "chunkResults" JSONB,
    "error" TEXT,

    CONSTRAINT "DeepAudit_pkey" PRIMARY KEY ("id")
);

-- AddForeign<PERSON><PERSON>
ALTER TABLE "DeepAudit" ADD CONSTRAINT "DeepAudit_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
