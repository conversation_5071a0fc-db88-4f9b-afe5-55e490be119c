import { NextRequest, NextResponse } from 'next/server';
import { openrouterChatCompletion, DEEPSEEK_MODEL, LLAMA4_MAVERICK_MODEL } from '@/lib/deepseek';

const SYSTEM_PROMPT = `You are an expert code reviewer AI. Given a pull request diff and a user question, answer the question as helpfully and concisely as possible. Use the PR context to provide accurate, actionable, and clear answers. If the question is not related to the PR, politely say so.`;

export async function POST(req: NextRequest) {
  try {
    const { diff, prTitle, prDescription, question } = await req.json();
    if (!diff || !question) {
      return NextResponse.json({ error: 'Missing diff or question' }, { status: 400 });
    }

    // Model selection logic (same as review-pr)
    const DEEPSEEK_MAX_CHARS = 100000;
    let model = DEEPSEEK_MODEL;
    if (diff.length > DEEPSEEK_MAX_CHARS) {
      model = LLAMA4_MAVERICK_MODEL;
    }
    const LLAMA4_MAX_CHARS = 900000;
    let safeDiff = diff;
    if (model === LLAMA4_MAVERICK_MODEL && diff.length > LLAMA4_MAX_CHARS) {
      safeDiff = diff.slice(0, LLAMA4_MAX_CHARS);
    }

    const userPrompt = `Pull Request Title: ${prTitle || ''}
Description: ${prDescription || ''}

Diff:
${safeDiff}

User Question: ${question}`;

    const aiResponse = await openrouterChatCompletion(
      [
        { role: 'system', content: SYSTEM_PROMPT },
        { role: 'user', content: userPrompt }
      ],
      model
    );

    return NextResponse.json({ answer: aiResponse });
  } catch (error: any) {
    console.error('AI Q&A error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 