import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session || !session.accessToken) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const { searchParams } = new URL(req.url);
  const owner = searchParams.get('owner');
  const repo = searchParams.get('repo');
  const number = searchParams.get('number');
  if (!owner || !repo || !number) {
    return NextResponse.json({ error: 'Missing parameters' }, { status: 400 });
  }
  try {
    // Fetch PR metadata
    const prRes = await fetch(`https://api.github.com/repos/${owner}/${repo}/pulls/${number}`, {
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
        Accept: 'application/vnd.github+json',
      },
    });
    if (prRes.status === 401 || prRes.status === 403) {
      return NextResponse.json({ error: 'GitHub token expired or insufficient permissions.', reauth: true }, { status: 401 });
    }
    if (prRes.status === 404) {
      return NextResponse.json({ error: 'Pull request not found.' }, { status: 404 });
    }
    if (prRes.status === 429) {
      return NextResponse.json({ error: 'GitHub API rate limit exceeded.' }, { status: 429 });
    }
    if (!prRes.ok) {
      return NextResponse.json({ error: 'Failed to fetch PR metadata.' }, { status: 500 });
    }
    const pr = await prRes.json();
    // Fetch PR diff
    const diffRes = await fetch(`https://api.github.com/repos/${owner}/${repo}/pulls/${number}`, {
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
        Accept: 'application/vnd.github.v3.diff',
      },
    });
    if (!diffRes.ok) {
      return NextResponse.json({ error: 'Failed to fetch PR diff.' }, { status: 500 });
    }
    const diff = await diffRes.text();
    return NextResponse.json({
      diff,
      title: pr.title,
      body: pr.body,
      user: pr.user?.login,
      head: pr.head,
      base: pr.base,
    });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 