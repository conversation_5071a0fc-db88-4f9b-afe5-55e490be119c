# Dashboard Real-Time Statistics

## Overview
The dashboard now displays real-time statistics based on actual user data from the database.

## Statistics Cards

### 1. Monitored Repositories
- **Data Source**: `MonitoredRepository` table
- **Shows**: Total number of repositories the user has selected to monitor
- **Empty State**: "No repositories monitored"

### 2. Total Pull Requests
- **Data Source**: `PullRequestReview` + `CodeQualityReview` tables
- **Shows**: Total number of pull request reviews completed across all monitored repositories
- **Empty State**: "No reviews yet"

### 3. Deep Audits Run
- **Data Source**: `DeepAudit` table
- **Shows**: Total number of comprehensive AI audits the user has initiated
- **Empty State**: "No audits yet"

### 4. Total Issues Found
- **Data Source**: `ReviewStats` table (aggregated)
- **Shows**: Sum of all issues found across categories:
  - Bug issues
  - Security issues
  - Performance issues
  - Maintainability issues
- **Empty State**: "No issues detected"

## Features

### Real-Time Updates
- Statistics are fetched when the dashboard loads
- Manual refresh button with loading animation
- Automatic loading states for better UX

### Getting Started Guide
- When no repositories are monitored, shows a helpful call-to-action
- Direct link to repository selection page
- Clear instructions for new users

### API Endpoint
- **Endpoint**: `/api/dashboard/stats`
- **Method**: GET
- **Authentication**: Required (NextAuth session)
- **Response**: JSON object with all four statistics

## Database Schema Dependencies

The dashboard statistics rely on the following Prisma models:
- `User` - For user identification
- `MonitoredRepository` - For repository count
- `PullRequestReview` - For PR review count
- `CodeQualityReview` - For code quality review count
- `DeepAudit` - For audit count
- `ReviewStats` - For issue statistics

## Usage

1. User logs in and navigates to `/dashboard`
2. Dashboard automatically fetches real statistics
3. If no repositories are monitored, shows getting started guide
4. User can manually refresh statistics using the refresh button
5. Statistics update in real-time as user performs actions (reviews, audits, etc.)
