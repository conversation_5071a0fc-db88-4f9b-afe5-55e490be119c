import { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  Target,
  BarChart3,
  Shield,
  Code,
  FileText,
  TestTube,
  AlertTriangle,
  CheckCircle,
  Activity
} from 'lucide-react';

interface CodeQualityScoreProps {
  repositoryId: string;
  pullRequestId?: string;
}

interface ScoreData {
  score: number;
  codeComplexity: number;
  testCoverage: number;
  codeStyle: number;
  securityScore: number;
  documentationScore: number;
  createdAt: string;
}

export default function CodeQualityScore({
  repositoryId,
  pullRequestId,
}: CodeQualityScoreProps) {
  const [scoreData, setScoreData] = useState<ScoreData[]>([]);
  const [averageScore, setAverageScore] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [trends, setTrends] = useState<{[key: string]: number}>({});

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const timeRange = {
          start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          end: new Date(),
        };

        const response = await fetch(
          `/api/code-quality?repositoryId=${repositoryId}&timeRange=${JSON.stringify(
            timeRange
          )}`
        );

        if (!response.ok) {
          throw new Error('Failed to fetch code quality data');
        }

        const data = await response.json();
        setScoreData(data);

        // Calculate average score
        const avgScore = data.length > 0
          ? data.reduce((acc: number, curr: ScoreData) => acc + curr.score, 0) / data.length
          : 0;
        setAverageScore(avgScore);

        // Calculate trends (compare last 7 days vs previous 7 days)
        if (data.length >= 2) {
          const now = new Date();
          const last7Days = data.filter((item: ScoreData) => {
            const itemDate = new Date(item.createdAt);
            const daysDiff = (now.getTime() - itemDate.getTime()) / (1000 * 60 * 60 * 24);
            return daysDiff <= 7;
          });

          const previous7Days = data.filter((item: ScoreData) => {
            const itemDate = new Date(item.createdAt);
            const daysDiff = (now.getTime() - itemDate.getTime()) / (1000 * 60 * 60 * 24);
            return daysDiff > 7 && daysDiff <= 14;
          });

          const calculateTrend = (recent: ScoreData[], previous: ScoreData[], key: keyof ScoreData) => {
            if (recent.length === 0 || previous.length === 0) return 0;
            const recentAvg = recent.reduce((acc, curr) => acc + (curr[key] as number), 0) / recent.length;
            const previousAvg = previous.reduce((acc, curr) => acc + (curr[key] as number), 0) / previous.length;
            return ((recentAvg - previousAvg) / previousAvg) * 100;
          };

          setTrends({
            score: calculateTrend(last7Days, previous7Days, 'score'),
            codeComplexity: calculateTrend(last7Days, previous7Days, 'codeComplexity'),
            testCoverage: calculateTrend(last7Days, previous7Days, 'testCoverage'),
            codeStyle: calculateTrend(last7Days, previous7Days, 'codeStyle'),
            securityScore: calculateTrend(last7Days, previous7Days, 'securityScore'),
            documentationScore: calculateTrend(last7Days, previous7Days, 'documentationScore'),
          });
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [repositoryId]);

  if (loading) {
    return (
      <div
        className="p-6 rounded-lg"
        style={{
          backgroundColor: 'var(--card)',
          border: '1px solid var(--border)'
        }}
      >
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style={{borderColor: 'var(--primary)'}}></div>
          <p style={{color: 'var(--muted-foreground)'}}>Loading code quality data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className="p-6 rounded-lg"
        style={{
          backgroundColor: 'var(--card)',
          border: '1px solid var(--border)'
        }}
      >
        <div className="text-center py-8">
          <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-red-400" />
          <p className="text-lg font-medium mb-2" style={{color: 'var(--foreground)'}}>Failed to Load Quality Data</p>
          <p style={{color: 'var(--muted-foreground)'}}>{error}</p>
        </div>
      </div>
    );
  }

  // Helper function to get trend icon and color
  const getTrendDisplay = (trend: number) => {
    if (Math.abs(trend) < 1) {
      return { icon: Activity, color: 'text-gray-400', text: '0%' };
    } else if (trend > 0) {
      return { icon: TrendingUp, color: 'text-green-400', text: `+${trend.toFixed(1)}%` };
    } else {
      return { icon: TrendingDown, color: 'text-red-400', text: `${trend.toFixed(1)}%` };
    }
  };

  // Get latest scores for individual metrics
  const latestData = scoreData.length > 0 ? scoreData[scoreData.length - 1] : null;

  return (
    <div className="space-y-6">
      {/* Code Quality Overview */}
      <section
        className="rounded-lg p-6"
        style={{
          backgroundColor: 'var(--card)',
          border: '1px solid var(--border)'
        }}
      >
        <div className="flex items-center space-x-2 mb-6">
          <Target className="h-6 w-6" style={{color: 'var(--primary)'}} />
          <h2 className="text-2xl font-bold" style={{color: 'var(--foreground)'}}>
            Code Quality Overview
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Overall Score Card */}
          <div
            className="rounded-lg p-6 border"
            style={{
              backgroundColor: 'var(--background)',
              borderColor: 'var(--border)'
            }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5 text-blue-400" />
                <h3 className="font-semibold" style={{color: 'var(--foreground)'}}>Overall Score</h3>
              </div>
              {trends.score !== undefined && (
                <div className="flex items-center space-x-1">
                  {(() => {
                    const trendDisplay = getTrendDisplay(trends.score);
                    const TrendIcon = trendDisplay.icon;
                    return (
                      <>
                        <TrendIcon className={`h-4 w-4 ${trendDisplay.color}`} />
                        <span className={`text-xs font-medium ${trendDisplay.color}`}>
                          {trendDisplay.text}
                        </span>
                      </>
                    );
                  })()}
                </div>
              )}
            </div>
            <div className="flex items-end space-x-2">
              <span className="text-3xl font-bold" style={{color: 'var(--foreground)'}}>
                {scoreData.length > 0 ? averageScore.toFixed(1) : '0'}
              </span>
              <span className="text-sm pb-1" style={{color: 'var(--muted-foreground)'}}>/100</span>
            </div>
            <p className="text-xs mt-1" style={{color: 'var(--muted-foreground)'}}>
              Average across {scoreData.length} reviews
            </p>
          </div>

          {/* Latest Score Card */}
          <div
            className="rounded-lg p-6 border"
            style={{
              backgroundColor: 'var(--background)',
              borderColor: 'var(--border)'
            }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-400" />
                <h3 className="font-semibold" style={{color: 'var(--foreground)'}}>Latest Score</h3>
              </div>
            </div>
            <div className="flex items-end space-x-2">
              <span className="text-3xl font-bold" style={{color: 'var(--foreground)'}}>
                {latestData ? latestData.score.toFixed(1) : 'N/A'}
              </span>
              {latestData && <span className="text-sm pb-1" style={{color: 'var(--muted-foreground)'}}>/100</span>}
            </div>
            <p className="text-xs mt-1" style={{color: 'var(--muted-foreground)'}}>
              {latestData ? new Date(latestData.createdAt).toLocaleDateString() : 'No data available'}
            </p>
          </div>

          {/* Total Reviews Card */}
          <div
            className="rounded-lg p-6 border"
            style={{
              backgroundColor: 'var(--background)',
              borderColor: 'var(--border)'
            }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Activity className="h-5 w-5 text-purple-400" />
                <h3 className="font-semibold" style={{color: 'var(--foreground)'}}>Total Reviews</h3>
              </div>
            </div>
            <div className="flex items-end space-x-2">
              <span className="text-3xl font-bold" style={{color: 'var(--foreground)'}}>
                {scoreData.length}
              </span>
            </div>
            <p className="text-xs mt-1" style={{color: 'var(--muted-foreground)'}}>
              Last 30 days
            </p>
          </div>
        </div>

        {/* Detailed Metrics */}
        {latestData && (
          <div className="mt-6 grid grid-cols-2 md:grid-cols-5 gap-4">
            {[
              { key: 'codeComplexity', label: 'Code Complexity', icon: Code, color: 'text-blue-400' },
              { key: 'testCoverage', label: 'Test Coverage', icon: TestTube, color: 'text-green-400' },
              { key: 'codeStyle', label: 'Code Style', icon: FileText, color: 'text-purple-400' },
              { key: 'securityScore', label: 'Security', icon: Shield, color: 'text-red-400' },
              { key: 'documentationScore', label: 'Documentation', icon: FileText, color: 'text-yellow-400' }
            ].map(({ key, label, icon: Icon, color }) => {
              const value = latestData[key as keyof ScoreData] as number;
              const trend = trends[key] || 0;
              const trendDisplay = getTrendDisplay(trend);
              const TrendIcon = trendDisplay.icon;

              return (
                <div
                  key={key}
                  className="rounded-lg p-4 border"
                  style={{
                    backgroundColor: 'var(--accent)',
                    borderColor: 'var(--border)'
                  }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <Icon className={`h-4 w-4 ${color}`} />
                    <div className="flex items-center space-x-1">
                      <TrendIcon className={`h-3 w-3 ${trendDisplay.color}`} />
                      <span className={`text-xs ${trendDisplay.color}`}>
                        {trendDisplay.text}
                      </span>
                    </div>
                  </div>
                  <div className="text-lg font-bold" style={{color: 'var(--foreground)'}}>
                    {value.toFixed(1)}
                  </div>
                  <div className="text-xs" style={{color: 'var(--muted-foreground)'}}>
                    {label}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </section>

      {/* Quality Trends Chart */}
      <section
        className="rounded-lg p-6"
        style={{
          backgroundColor: 'var(--card)',
          border: '1px solid var(--border)'
        }}
      >
        <div className="flex items-center space-x-2 mb-6">
          <TrendingUp className="h-6 w-6" style={{color: 'var(--primary)'}} />
          <h3 className="text-2xl font-bold" style={{color: 'var(--foreground)'}}>
            Quality Trends
          </h3>
        </div>

        {scoreData.length === 0 ? (
          <div className="text-center py-12">
            <BarChart3 className="h-12 w-12 mx-auto mb-3 opacity-50" style={{color: 'var(--muted-foreground)'}} />
            <p className="text-lg font-medium mb-1" style={{color: 'var(--foreground)'}}>No trend data available</p>
            <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
              Quality trends will appear here after multiple reviews
            </p>
          </div>
        ) : (
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={scoreData}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(75, 85, 99, 0.3)" />
                <XAxis
                  dataKey="createdAt"
                  tickFormatter={(date: string) =>
                    new Date(date).toLocaleDateString()
                  }
                  tick={{ fill: 'rgb(156, 163, 175)', fontSize: 12 }}
                />
                <YAxis
                  domain={[0, 100]}
                  tick={{ fill: 'rgb(156, 163, 175)', fontSize: 12 }}
                />
                <Tooltip
                  labelFormatter={(date: string) =>
                    new Date(date).toLocaleDateString()
                  }
                  contentStyle={{
                    backgroundColor: 'var(--card)',
                    border: '1px solid var(--border)',
                    borderRadius: '8px',
                    color: 'var(--foreground)'
                  }}
                />
                <Legend
                  wrapperStyle={{ color: 'rgb(156, 163, 175)' }}
                />
                <Line
                  type="monotone"
                  dataKey="score"
                  stroke="#3b82f6"
                  strokeWidth={3}
                  name="Overall Score"
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
                />
                <Line
                  type="monotone"
                  dataKey="codeComplexity"
                  stroke="#10b981"
                  strokeWidth={2}
                  name="Code Complexity"
                  dot={{ fill: '#10b981', strokeWidth: 2, r: 3 }}
                />
                <Line
                  type="monotone"
                  dataKey="testCoverage"
                  stroke="#ef4444"
                  strokeWidth={2}
                  name="Test Coverage"
                  dot={{ fill: '#ef4444', strokeWidth: 2, r: 3 }}
                />
                <Line
                  type="monotone"
                  dataKey="codeStyle"
                  stroke="#8b5cf6"
                  strokeWidth={2}
                  name="Code Style"
                  dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 3 }}
                />
                <Line
                  type="monotone"
                  dataKey="securityScore"
                  stroke="#f59e0b"
                  strokeWidth={2}
                  name="Security"
                  dot={{ fill: '#f59e0b', strokeWidth: 2, r: 3 }}
                />
                <Line
                  type="monotone"
                  dataKey="documentationScore"
                  stroke="#06b6d4"
                  strokeWidth={2}
                  name="Documentation"
                  dot={{ fill: '#06b6d4', strokeWidth: 2, r: 3 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        )}
      </section>
    </div>
  );
}