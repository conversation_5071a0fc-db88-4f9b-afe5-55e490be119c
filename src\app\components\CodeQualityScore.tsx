import { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';

interface CodeQualityScoreProps {
  repositoryId: string;
  pullRequestId?: string;
}

interface ScoreData {
  score: number;
  codeComplexity: number;
  testCoverage: number;
  codeStyle: number;
  securityScore: number;
  documentationScore: number;
  createdAt: string;
}

export default function CodeQualityScore({
  repositoryId,
  pullRequestId,
}: CodeQualityScoreProps) {
  const [scoreData, setScoreData] = useState<ScoreData[]>([]);
  const [averageScore, setAverageScore] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const timeRange = {
          start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          end: new Date(),
        };

        const response = await fetch(
          `/api/code-quality?repositoryId=${repositoryId}&timeRange=${JSON.stringify(
            timeRange
          )}`
        );

        if (!response.ok) {
          throw new Error('Failed to fetch code quality data');
        }

        const data = await response.json();
        setScoreData(data);
        setAverageScore(
          data.length > 0
            ? data.reduce((acc: number, curr: ScoreData) => acc + curr.score, 0) / data.length
            : 0
        );
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [repositoryId]);

  if (loading) {
    return <div>Loading code quality data...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="p-4">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">Code Quality Overview</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-2">Average Score</h3>
            <p className="text-3xl font-bold text-blue-600">
              {scoreData.length > 0 ? averageScore.toFixed(1) : 0}
            </p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-2">Total Reviews</h3>
            <p className="text-3xl font-bold text-green-600">
              {scoreData.length}
            </p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-2">Latest Score</h3>
            <p className="text-3xl font-bold text-purple-600">
              {scoreData.length > 0 ? scoreData[scoreData.length - 1]?.score.toFixed(1) : 'N/A'}
            </p>
          </div>
        </div>
      </div>

      <div className="bg-white p-4 rounded-lg shadow">
        <h3 className="text-xl font-semibold mb-4">Quality Trends</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={scoreData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="createdAt"
                tickFormatter={(date: string) =>
                  new Date(date).toLocaleDateString()
                }
              />
              <YAxis domain={[0, 100]} />
              <Tooltip
                labelFormatter={(date: string) =>
                  new Date(date).toLocaleDateString()
                }
              />
              <Legend />
              <Line
                type="monotone"
                dataKey="score"
                stroke="#2563eb"
                name="Overall Score"
              />
              <Line
                type="monotone"
                dataKey="codeComplexity"
                stroke="#16a34a"
                name="Code Complexity"
              />
              <Line
                type="monotone"
                dataKey="testCoverage"
                stroke="#dc2626"
                name="Test Coverage"
              />
              <Line
                type="monotone"
                dataKey="codeStyle"
                stroke="#9333ea"
                name="Code Style"
              />
              <Line
                type="monotone"
                dataKey="securityScore"
                stroke="#ea580c"
                name="Security"
              />
              <Line
                type="monotone"
                dataKey="documentationScore"
                stroke="#0891b2"
                name="Documentation"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
} 