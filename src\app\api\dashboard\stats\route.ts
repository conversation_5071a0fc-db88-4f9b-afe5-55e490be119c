import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session || !session.user?.email) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Find user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: {
        monitoredRepos: true,
        deepAudits: true,
      },
    });

    if (!user) {
      return NextResponse.json({
        monitoredRepos: 0,
        totalPullRequests: 0,
        totalDeepAudits: 0,
        totalIssuesFound: 0,
      });
    }

    // Count monitored repositories
    const monitoredRepos = user.monitoredRepos.length;

    // Count total deep audits
    const totalDeepAudits = user.deepAudits.length;

    // Count total pull request reviews
    const totalPullRequestReviews = await prisma.pullRequestReview.count({
      where: {
        repositoryId: {
          in: user.monitoredRepos.map(repo => repo.id)
        }
      }
    });

    // Count total code quality reviews
    const totalCodeQualityReviews = await prisma.codeQualityReview.count({
      where: {
        repositoryId: {
          in: user.monitoredRepos.map(repo => repo.id)
        }
      }
    });

    // Total reviews (PR reviews + code quality reviews)
    const totalPullRequests = totalPullRequestReviews + totalCodeQualityReviews;

    // Count total issues found from review stats
    const reviewStats = await prisma.reviewStats.findMany({
      where: {
        userId: user.id
      }
    });

    const totalIssuesFound = reviewStats.reduce((total, stat) => {
      return total + stat.bugsFound + stat.securityIssues + stat.performanceIssues + stat.maintainabilityIssues;
    }, 0);

    return NextResponse.json({
      monitoredRepos,
      totalPullRequests,
      totalDeepAudits,
      totalIssuesFound,
    });

  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
