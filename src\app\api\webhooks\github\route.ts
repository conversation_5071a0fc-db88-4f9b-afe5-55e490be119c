import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';

// Verify GitHub webhook signature
function verifySignature(payload: string, signature: string, secret: string): boolean {
  const hmac = crypto.createHmac('sha256', secret);
  const digest = hmac.update(payload).digest('hex');
  return `sha256=${digest}` === signature;
}

export async function POST(req: NextRequest) {
  try {
    const payload = await req.text();
    const signature = req.headers.get('x-hub-signature-256');
    const event = req.headers.get('x-github-event');

    // Verify GitHub signature
    const webhookSecret = process.env.GITHUB_WEBHOOK_SECRET;
    if (!webhookSecret || !signature || !verifySignature(payload, signature, webhookSecret)) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    }

    // Parse the payload
    const data = JSON.parse(payload);
    const action = data.action;
    const pr = data.pull_request;

    // Only process PR events: opened, synchronize, reopened
    if (event === 'pull_request' && ['opened', 'synchronize', 'reopened'].includes(action)) {
      const owner = pr.base.repo.owner.login;
      const repo = pr.base.repo.name;
      const number = pr.number;

      // Fetch PR diff
      const diffRes = await fetch(`https://api.github.com/repos/${owner}/${repo}/pulls/${number}`, {
        headers: {
          Accept: 'application/vnd.github.v3.diff',
          Authorization: `Bearer ${process.env.GITHUB_ACCESS_TOKEN}`,
        },
      });
      if (!diffRes.ok) {
        return NextResponse.json({ error: 'Failed to fetch PR diff' }, { status: 500 });
      }
      const diff = await diffRes.text();

      // Run AI review
      const reviewRes = await fetch(`${req.nextUrl.origin}/api/ai/review-pr`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ diff, prTitle: pr.title, prDescription: pr.body }),
      });
      if (!reviewRes.ok) {
        return NextResponse.json({ error: 'Failed to run AI review' }, { status: 500 });
      }
      const { review } = await reviewRes.json();

      // Post review as a comment on the PR
      const commentRes = await fetch(`https://api.github.com/repos/${owner}/${repo}/issues/${number}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.GITHUB_ACCESS_TOKEN}`,
        },
        body: JSON.stringify({ body: review }),
      });
      if (!commentRes.ok) {
        return NextResponse.json({ error: 'Failed to post review comment' }, { status: 500 });
      }
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 