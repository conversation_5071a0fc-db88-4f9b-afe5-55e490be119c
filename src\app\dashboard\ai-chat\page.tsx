'use client';

import { useState } from 'react';

export default function AIChatPage() {
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState<{ role: 'user' | 'assistant'; content: string }[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;
    setMessages(msgs => [...msgs, { role: 'user', content: input }]);
    setLoading(true);
    setError(null);
    try {
      const res = await fetch('/api/ai/test', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        cache: 'no-store',
        next: { revalidate: 0 },
        // Pass prompt as query param
      } as any);
      const data = await fetch(`/api/ai/test?prompt=${encodeURIComponent(input)}`);
      const json = await data.json();
      if (json.result) {
        setMessages(msgs => [...msgs, { role: 'assistant', content: json.result }]);
      } else {
        setError(json.error || 'Unknown error');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
      setInput('');
    }
  };

  return (
    <div className="p-8 max-w-2xl mx-auto">
      <h2 className="text-3xl font-bold mb-4 text-white">AI Chat (Deepseek Test)</h2>
      <div className="bg-white rounded-lg shadow p-6 min-h-[300px] flex flex-col">
        <div className="flex-1 space-y-4 mb-4 overflow-y-auto max-h-96">
          {messages.length === 0 && <div className="text-gray-400">Start a conversation with DeepSeek V3 0324...</div>}
          {messages.map((msg, i) => (
            <div key={i} className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`px-4 py-2 rounded-lg ${msg.role === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-900'}`}>{msg.content}</div>
            </div>
          ))}
        </div>
        {error && <div className="text-red-600 mb-2">{error}</div>}
        <form onSubmit={sendMessage} className="flex gap-2 mt-2">
          <input
            type="text"
            value={input}
            onChange={e => setInput(e.target.value)}
            className="flex-1 px-3 py-2 border rounded focus:outline-none focus:ring focus:border-blue-400"
            placeholder="Type your message..."
            disabled={loading}
          />
          <button
            type="submit"
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
            disabled={loading || !input.trim()}
          >
            {loading ? 'Sending...' : 'Send'}
          </button>
        </form>
      </div>
    </div>
  );
} 