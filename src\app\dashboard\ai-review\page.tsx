'use client';

import { useState } from 'react';

export default function AIReviewPage() {
  const [prTitle, setPrTitle] = useState('Add new feature X');
  const [prDescription, setPrDescription] = useState('This PR adds feature X and refactors Y.');
  const [diff, setDiff] = useState('diff --git a/file.js b/file.js\nindex 123..456 100644\n--- a/file.js\n+++ b/file.js\n@@ ...\n+const x = 42;\n');
  const [review, setReview] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setReview('');
    try {
      const res = await fetch('/api/ai/review-pr', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prTitle, prDescription, diff }),
      });
      const data = await res.json();
      if (data.review) {
        setReview(data.review);
      } else {
        setError(data.error || 'Unknown error');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8 max-w-3xl mx-auto">
      <h2 className="text-3xl font-bold mb-4 text-white">AI Code Review (DeepSeek V3 0324)</h2>
      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6 space-y-4 mb-6">
        <div>
          <label className="block font-semibold mb-1">PR Title</label>
          <input
            type="text"
            value={prTitle}
            onChange={e => setPrTitle(e.target.value)}
            className="w-full px-3 py-2 border rounded"
            required
          />
        </div>
        <div>
          <label className="block font-semibold mb-1">PR Description</label>
          <textarea
            value={prDescription}
            onChange={e => setPrDescription(e.target.value)}
            className="w-full px-3 py-2 border rounded"
            rows={2}
          />
        </div>
        <div>
          <label className="block font-semibold mb-1">PR Diff</label>
          <textarea
            value={diff}
            onChange={e => setDiff(e.target.value)}
            className="w-full px-3 py-2 border rounded font-mono text-xs"
            rows={8}
            required
          />
        </div>
        <button
          type="submit"
          className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50 font-semibold"
          disabled={loading}
        >
          {loading ? 'Reviewing...' : 'Run AI Review'}
        </button>
      </form>
      {error && <div className="text-red-600 mb-4">{error}</div>}
      {review && (
        <div className="bg-gray-900 text-white rounded-lg shadow p-6 whitespace-pre-wrap overflow-x-auto">
          <h3 className="text-xl font-semibold mb-2 text-blue-300">AI Review Result</h3>
          <div className="prose prose-invert max-w-none" dangerouslySetInnerHTML={{ __html: review.replace(/\n/g, '<br/>') }} />
        </div>
      )}
    </div>
  );
} 