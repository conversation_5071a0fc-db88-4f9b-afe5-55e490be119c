import { NextResponse } from 'next/server';
import { calculateCodeQualityScore, getQualityTrends, getRepositoryAverageScore } from '@/lib/codeQuality';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      console.error('Unauthorized request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { pullRequestId, repositoryId, codeDiff } = body;
    console.log('Received code quality POST:', { pullRequestId, repositoryId, codeDiffLength: codeDiff?.length });

    if (!pullRequestId || !repositoryId || !codeDiff) {
      console.error('Missing required fields', { pullRequestId, repositoryId, codeDiff });
      return NextResponse.json(
        { error: 'Missing required fields', details: { pullRequestId, repositoryId, codeDiff } },
        { status: 400 }
      );
    }

    const result = await calculateCodeQualityScore(
      pullRequestId,
      repositoryId,
      codeDiff
    );

    console.log('Code quality score stored for PR:', pullRequestId, 'Repo:', repositoryId);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error calculating code quality score:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : error },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const repositoryId = searchParams.get('repositoryId');
    const timeRange = searchParams.get('timeRange');

    if (!repositoryId) {
      return NextResponse.json(
        { error: 'Repository ID is required' },
        { status: 400 }
      );
    }

    if (timeRange) {
      const { start, end } = JSON.parse(timeRange);
      const trends = await getQualityTrends(repositoryId, {
        start: new Date(start),
        end: new Date(end),
      });
      return NextResponse.json(trends);
    }

    const averageScore = await getRepositoryAverageScore(repositoryId);
    return NextResponse.json({ averageScore });
  } catch (error) {
    console.error('Error fetching code quality data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 