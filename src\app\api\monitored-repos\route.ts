import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session || !session.user?.email) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { repos } = await req.json();
  if (!Array.isArray(repos)) {
    return NextResponse.json({ error: 'Invalid data' }, { status: 400 });
  }

  // Find or create the user by email
  let user = await prisma.user.findUnique({ where: { email: session.user.email } });
  if (!user) {
    user = await prisma.user.create({
      data: {
        email: session.user.email,
        name: session.user.name,
        image: session.user.image,
      },
    });
  }

  // Remove previous monitored repos for this user
  await prisma.monitoredRepository.deleteMany({ where: { userId: user.id } });

  // Add new monitored repos
  await prisma.monitoredRepository.createMany({
    data: repos.map((repo: any) => ({
      repoId: String(repo.id),
      repoName: repo.name,
      repoOwner: repo.owner,
      userId: user.id,
    })),
  });

  return NextResponse.json({ success: true });
}

export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session || !session.user?.email) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const user = await prisma.user.findUnique({
    where: { email: session.user.email },
    include: { monitoredRepos: true },
  });

  if (!user) {
    return NextResponse.json([]);
  }

  return NextResponse.json(user.monitoredRepos);
} 