import { NextRequest, NextResponse } from 'next/server';
import { deepseekChatCompletion } from '@/lib/deepseek';

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const prompt = searchParams.get('prompt') || 'What is the meaning of life?';
  try {
    const aiResponse = await deepseekChatCompletion([
      { role: 'user', content: prompt }
    ]);
    return NextResponse.json({ result: aiResponse });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 