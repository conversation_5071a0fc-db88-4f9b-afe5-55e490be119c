-- CreateTable
CREATE TABLE "CodeQualityScore" (
    "id" TEXT NOT NULL,
    "pullRequestId" TEXT NOT NULL,
    "repositoryId" TEXT NOT NULL,
    "score" DOUBLE PRECISION NOT NULL,
    "codeComplexity" DOUBLE PRECISION NOT NULL,
    "testCoverage" DOUBLE PRECISION NOT NULL,
    "codeStyle" DOUBLE PRECISION NOT NULL,
    "securityScore" DOUBLE PRECISION NOT NULL,
    "documentationScore" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CodeQualityScore_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "CodeQualityScore_repositoryId_idx" ON "CodeQualityScore"("repositoryId");

-- CreateIndex
CREATE INDEX "CodeQualityScore_createdAt_idx" ON "CodeQualityScore"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "CodeQualityScore_pullRequestId_key" ON "CodeQualityScore"("pullRequestId");

-- AddForeignKey
ALTER TABLE "CodeQualityScore" ADD CONSTRAINT "CodeQualityScore_repositoryId_fkey" FOREIGN KEY ("repositoryId") REFERENCES "MonitoredRepository"("id") ON DELETE CASCADE ON UPDATE CASCADE;
