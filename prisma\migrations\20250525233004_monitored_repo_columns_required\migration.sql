/*
  Warnings:

  - Made the column `repoId` on table `MonitoredRepository` required. This step will fail if there are existing NULL values in that column.
  - Made the column `repoName` on table `MonitoredRepository` required. This step will fail if there are existing NULL values in that column.
  - Made the column `repoOwner` on table `MonitoredRepository` required. This step will fail if there are existing NULL values in that column.
  - Made the column `updatedAt` on table `MonitoredRepository` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "MonitoredRepository" ALTER COLUMN "repoId" SET NOT NULL,
ALTER COLUMN "repoName" SET NOT NULL,
ALTER COLUMN "repoOwner" SET NOT NULL,
ALTER COLUMN "updatedAt" SET NOT NULL;
