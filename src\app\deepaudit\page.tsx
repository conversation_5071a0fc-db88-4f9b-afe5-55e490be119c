"use client";

import { useEffect, useState, useRef } from "react";
import Link from "next/link";

interface Repo {
  id: number;
  name: string;
  full_name: string;
  owner: string;
  private: boolean;
  html_url: string;
  description: string;
}

interface AuditJob {
  id: string;
  repoName: string;
  repoOwner: string;
  status: string;
  progress: number;
  startedAt: string;
  completedAt?: string;
}

export default function DeepAuditPage() {
  const [repos, setRepos] = useState<Repo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [auditLoading, setAuditLoading] = useState<number | null>(null);
  const [auditStarted, setAuditStarted] = useState<string | null>(null);
  const [auditInfo, setAuditInfo] = useState<any | null>(null);
  const [auditId, setAuditId] = useState<string | null>(null);
  const [polling, setPolling] = useState(false);
  const [progress, setProgress] = useState<number>(0);
  const [status, setStatus] = useState<string | null>(null);
  const [report, setReport] = useState<any | null>(null);
  const [chunkResults, setChunkResults] = useState<any[] | null>(null);
  const [history, setHistory] = useState<AuditJob[]>([]);
  const [selectedPanel, setSelectedPanel] = useState<'new' | 'current' | 'history'>('new');
  const [selectedHistoryAudit, setSelectedHistoryAudit] = useState<AuditJob | null>(null);
  const pollRef = useRef<NodeJS.Timeout | null>(null);
  // Expand/collapse state for report sections
  const sectionNames = report && report.sections && typeof report.sections === 'object' ? Object.keys(report.sections) : [];
  const [openSections, setOpenSections] = useState<Record<string, boolean>>(() =>
    Object.fromEntries(sectionNames.map(name => [name, true]))
  );

  useEffect(() => {
    async function fetchRepos() {
      setLoading(true);
      setError(null);
      try {
        const res = await fetch("/api/github/repos");
        if (!res.ok) throw new Error("Failed to fetch repositories");
        const data = await res.json();
        setRepos(data);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    fetchRepos();
    fetchHistory();
    return () => {
      if (pollRef.current) clearInterval(pollRef.current);
    };
  }, []);

  async function fetchHistory() {
    // Fetch previous audits for the user
    const res = await fetch("/api/deepaudit/history");
    if (res.ok) {
      const data = await res.json();
      setHistory(data.audits || []);
    }
  }

  async function startAudit(repo: Repo) {
    setAuditLoading(repo.id);
    setAuditStarted(null);
    setError(null);
    setAuditInfo(null);
    setAuditId(null);
    setPolling(false);
    setProgress(0);
    setStatus(null);
    setReport(null);
    setChunkResults(null);
    setSelectedPanel('current');
    setSelectedHistoryAudit(null);
    try {
      const res = await fetch("/api/deepaudit/start", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          id: repo.id,
          name: repo.name,
          full_name: repo.full_name,
          owner: repo.owner,
          private: repo.private,
          html_url: repo.html_url,
          description: repo.description,
        }),
      });
      if (!res.ok) throw new Error("Failed to start DeepAudit");
      const data = await res.json();
      setAuditStarted(`Audit started for ${repo.full_name}. Audit ID: ${data.auditId || 'N/A'}`);
      setAuditId(data.auditId);
      setPolling(true);
      pollStatus(data.auditId);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setAuditLoading(null);
    }
  }

  function pollStatus(auditId: string) {
    if (pollRef.current) clearInterval(pollRef.current);
    pollRef.current = setInterval(async () => {
      const res = await fetch(`/api/deepaudit/status?id=${auditId}`);
      if (res.ok) {
        const data = await res.json();
        setStatus(data.status);
        setProgress(data.progress || 0);
        setReport(data.report);
        setChunkResults(data.chunkResults);
        if (data.status === 'completed' || data.status === 'failed') {
          setPolling(false);
          clearInterval(pollRef.current!);
          fetchHistory();
        }
      }
    }, 2000);
  }

  function viewAudit(audit: AuditJob) {
    setAuditId(audit.id);
    setPolling(false);
    setAuditStarted(`Viewing audit for ${audit.repoOwner}/${audit.repoName}`);
    setSelectedPanel('history');
    setSelectedHistoryAudit(audit);
    // Fetch the report for this audit
    fetch(`/api/deepaudit/status?id=${audit.id}`).then(async res => {
      if (res.ok) {
        const data = await res.json();
        setStatus(data.status);
        setProgress(data.progress || 0);
        setReport(data.report);
        setChunkResults(data.chunkResults);
      }
    });
  }

  // Main layout: left panel + main content
  return (
    <div className="flex min-h-screen">
      {/* Left Panel */}
      <aside className="w-64 bg-cyan-900 text-white flex flex-col py-8 px-4 space-y-4">
        <h2 className="text-2xl font-extrabold mb-8 text-center">DeepAudit</h2>
        <button
          className={`w-full py-2 px-4 rounded-lg text-left font-semibold transition ${selectedPanel === 'new' ? 'bg-cyan-700' : 'hover:bg-cyan-800'}`}
          onClick={() => { setSelectedPanel('new'); setSelectedHistoryAudit(null); }}
        >
          <span className="mr-2">📝</span> New Audit
        </button>
        <button
          className={`w-full py-2 px-4 rounded-lg text-left font-semibold transition ${selectedPanel === 'current' ? 'bg-cyan-700' : 'hover:bg-cyan-800'}`}
          onClick={() => { setSelectedPanel('current'); setSelectedHistoryAudit(null); }}
        >
          <span className="mr-2">📊</span> Current Audit
        </button>
        <button
          className={`w-full py-2 px-4 rounded-lg text-left font-semibold transition ${selectedPanel === 'history' ? 'bg-cyan-700' : 'hover:bg-cyan-800'}`}
          onClick={() => { setSelectedPanel('history'); setSelectedHistoryAudit(null); }}
        >
          <span className="mr-2">🕑</span> Previous Audits
        </button>
        <div className="flex-1" />
        <Link href="/dashboard" className="block text-center text-cyan-200 hover:underline font-medium mt-8">Back to Dashboard</Link>
      </aside>
      {/* Main Content */}
      <main className="flex-1 p-8 bg-gray-50 overflow-y-auto">
        {/* New Audit: Repo List */}
        {selectedPanel === 'new' && (
          <div>
            <h2 className="text-2xl font-bold mb-6 text-cyan-800">Select a Repository to Audit</h2>
            {loading ? (
              <div>Loading repositories...</div>
            ) : error ? (
              <div className="text-red-600">{error}</div>
            ) : (
              <ul className="space-y-3">
                {repos.map((repo) => (
                  <li key={repo.id} className="flex items-center gap-4 bg-white rounded p-4 shadow border border-cyan-200">
                    <div className="flex-1">
                      <div className="font-semibold text-lg">{repo.full_name} {repo.private && <span className="ml-2 text-xs text-gray-500">(Private)</span>}</div>
                      <div className="text-xs text-gray-600">{repo.description}</div>
                    </div>
                    <a href={repo.html_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 text-xs underline">View</a>
                    <button
                      className="bg-cyan-600 text-white px-4 py-2 rounded hover:bg-cyan-700 disabled:opacity-50"
                      onClick={() => startAudit(repo)}
                      disabled={!!auditLoading}
                    >
                      {auditLoading === repo.id ? 'Starting...' : 'Start DeepAudit'}
                    </button>
                  </li>
                ))}
              </ul>
            )}
          </div>
        )}
        {/* Current Audit: Progress/Report */}
        {selectedPanel === 'current' && (
          <div>
            {auditStarted && <div className="mb-4 text-green-700 font-semibold">{auditStarted}</div>}
            {polling && (
              <div className="mb-6 flex items-center gap-4">
                <div className="w-full bg-cyan-100 rounded-full h-4">
                  <div className="bg-cyan-600 h-4 rounded-full transition-all" style={{ width: `${progress}%` }}></div>
                </div>
                <span className="font-mono text-xs">{progress.toFixed(0)}%</span>
                <span className="text-cyan-700 font-semibold">{status}</span>
              </div>
            )}
            {status === 'completed' && report && (
              <AuditReport report={report} chunkResults={chunkResults} openSections={openSections} setOpenSections={setOpenSections} />
            )}
            {status === 'failed' && (
              <div className="mt-8 p-6 bg-red-50 border border-red-200 rounded text-red-700 font-semibold">Audit failed. Please try again later.</div>
            )}
            {!polling && !report && <div className="text-gray-500">No audit in progress.</div>}
          </div>
        )}
        {/* Previous Audits: List and Report */}
        {selectedPanel === 'history' && (
          <div>
            <h2 className="text-2xl font-bold mb-6 text-cyan-800">Previous Audits</h2>
            {history.length === 0 ? (
              <div className="text-gray-500">No previous audits found.</div>
            ) : (
              <ul className="space-y-3 mb-8">
                {history.map(audit => (
                  <li key={audit.id} className={`flex items-center gap-4 bg-white rounded p-4 shadow border border-cyan-200 cursor-pointer hover:bg-cyan-100 ${selectedHistoryAudit?.id === audit.id ? 'ring-2 ring-cyan-500' : ''}`}
                    onClick={() => viewAudit(audit)}
                  >
                    <div className="flex-1">
                      <div className="font-semibold text-lg">{audit.repoOwner}/{audit.repoName}</div>
                      <div className="text-xs text-gray-600">Started: {new Date(audit.startedAt).toLocaleString()}</div>
                      <div className="text-xs text-gray-600">Status: {audit.status}</div>
                    </div>
                  </li>
                ))}
              </ul>
            )}
            {selectedHistoryAudit && report && (
              <AuditReport report={report} chunkResults={chunkResults} openSections={openSections} setOpenSections={setOpenSections} />
            )}
            {!selectedHistoryAudit && <div className="text-gray-500">Select an audit to view its report.</div>}
          </div>
        )}
      </main>
    </div>
  );
}

// AuditReport component for rendering the report UI (reuse existing report rendering logic)
function AuditReport({ report, chunkResults, openSections, setOpenSections }: any) {
  const sectionNames = report && report.sections && typeof report.sections === 'object' ? Object.keys(report.sections) : [];
  return (
    <div className="mt-8">
      {/* Summary Card */}
      <div className="p-6 mb-6 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg shadow-lg text-white">
        <div className="text-3xl font-extrabold mb-2">Comprehensive DeepAudit Report</div>
        <div className="mb-2 text-lg font-semibold">{report.summary}</div>
        <div className="mb-2 font-bold">Total files: <span className="font-mono font-normal">{report.totalFiles}</span></div>
        <div className="mb-2 font-bold">Total chunks: <span className="font-mono font-normal">{report.totalChunks}</span></div>
      </div>
      {/* Critical Issues */}
      {Array.isArray(report.criticalIssues) && report.criticalIssues.length > 0 && (
        <div className="mb-8">
          <div className="text-2xl font-bold text-red-700 mb-4">Critical Code Issues</div>
          {report.criticalIssues.map((issue: string, idx: number) => (
            <div key={idx} className="bg-red-100 border-l-8 border-red-600 rounded-lg shadow p-6 mb-4">
              <div className="font-bold text-lg text-red-800 mb-2">Critical Code Issue</div>
              <div className="prose prose-red max-w-none text-red-900 whitespace-pre-line mb-2">{issue}</div>
            </div>
          ))}
        </div>
      )}
      {/* Render structured sections if available */}
      {report.sections && typeof report.sections === 'object' ? (
        <div className="grid grid-cols-1 gap-6 mt-6">
          {Object.entries(report.sections).map(([section, content], idx) => {
            // Assign color by section
            const colorMap: Record<string, string> = {
              'Executive Summary': 'from-cyan-100 to-cyan-50 border-cyan-400',
              'Code Quality Assessment': 'from-blue-100 to-blue-50 border-blue-400',
              'Security Analysis': 'from-red-100 to-red-50 border-red-400',
              'Performance Evaluation': 'from-yellow-100 to-yellow-50 border-yellow-400',
              'Testing & Coverage': 'from-green-100 to-green-50 border-green-400',
              'Documentation & Comments': 'from-purple-100 to-purple-50 border-purple-400',
              'Dependency Analysis': 'from-pink-100 to-pink-50 border-pink-400',
              'Architecture & Design': 'from-indigo-100 to-indigo-50 border-indigo-400',
              'Recommendations': 'from-orange-100 to-orange-50 border-orange-400',
            };
            const color = colorMap[section] || 'from-gray-100 to-gray-50 border-gray-300';
            const open = openSections[section] ?? true;
            return (
              <div key={section} className={`bg-gradient-to-r ${color} border-l-8 rounded-lg shadow p-6 transition-all`}>
                <div className="flex items-center justify-between cursor-pointer" onClick={() => setOpenSections((prev: any) => ({ ...prev, [section]: !prev[section] }))}>
                  <h3 className="text-2xl font-bold mb-2" style={{ color: color.includes('red') ? '#b91c1c' : color.includes('blue') ? '#2563eb' : color.includes('green') ? '#059669' : color.includes('orange') ? '#ea580c' : color.includes('purple') ? '#7c3aed' : color.includes('pink') ? '#db2777' : color.includes('indigo') ? '#4f46e5' : '#0e7490' }}>{section}</h3>
                  <button className="ml-2 text-lg font-bold text-gray-500 hover:text-gray-800 focus:outline-none">{open ? '−' : '+'}</button>
                </div>
                {open && (
                  <div className="prose max-w-none text-gray-900 whitespace-pre-line text-lg">{String(content)}</div>
                )}
              </div>
            );
          })}
        </div>
      ) : (
        <div className="mb-4 whitespace-pre-wrap text-gray-800 bg-cyan-100 p-2 rounded">{report.findings}</div>
      )}
      {chunkResults && (
        <div className="mt-6">
          <div className="font-semibold mb-2 text-cyan-800">AI Analysis Results (per chunk):</div>
          <ul className="space-y-4">
            {chunkResults.map((result: any, idx: number) => (
              <li key={idx} className="bg-white border border-cyan-200 rounded p-4 shadow">
                <div className="font-bold mb-1">Chunk {result.chunkIndex} ({result.fileCount} files, {result.totalTokens} tokens)</div>
                <pre className="whitespace-pre-wrap text-sm text-gray-800 bg-cyan-50 p-2 rounded overflow-x-auto">{result.summary}</pre>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
} 