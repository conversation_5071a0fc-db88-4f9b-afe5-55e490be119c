"use client";

import { useEffect, useState, useRef } from "react";
import Link from "next/link";
import {
  Search,
  Zap,
  Clock,
  FileText,
  Shield,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Play,
  Pause,
  RotateCcw,
  Eye,
  Download,
  Share2,
  ChevronDown,
  ChevronRight,
  Activity,
  BarChart3,
  Code,
  Lock,
  Bug,
  Lightbulb,
  ArrowLeft,
  GitBranch,
  Calendar,
  Users,
  Star
} from "lucide-react";

interface Repo {
  id: number;
  name: string;
  full_name: string;
  owner: string;
  private: boolean;
  html_url: string;
  description: string;
}

interface AuditJob {
  id: string;
  repoName: string;
  repoOwner: string;
  status: string;
  progress: number;
  startedAt: string;
  completedAt?: string;
}

export default function DeepAuditPage() {
  const [repos, setRepos] = useState<Repo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [auditLoading, setAuditLoading] = useState<number | null>(null);
  const [auditStarted, setAuditStarted] = useState<string | null>(null);
  const [auditInfo, setAuditInfo] = useState<any | null>(null);
  const [auditId, setAuditId] = useState<string | null>(null);
  const [polling, setPolling] = useState(false);
  const [progress, setProgress] = useState<number>(0);
  const [status, setStatus] = useState<string | null>(null);
  const [report, setReport] = useState<any | null>(null);
  const [chunkResults, setChunkResults] = useState<any[] | null>(null);
  const [history, setHistory] = useState<AuditJob[]>([]);
  const [selectedPanel, setSelectedPanel] = useState<'new' | 'current' | 'history'>('new');
  const [selectedHistoryAudit, setSelectedHistoryAudit] = useState<AuditJob | null>(null);
  const pollRef = useRef<NodeJS.Timeout | null>(null);
  // Expand/collapse state for report sections
  const sectionNames = report && report.sections && typeof report.sections === 'object' ? Object.keys(report.sections) : [];
  const [openSections, setOpenSections] = useState<Record<string, boolean>>(() =>
    Object.fromEntries(sectionNames.map(name => [name, true]))
  );

  useEffect(() => {
    async function fetchRepos() {
      setLoading(true);
      setError(null);
      try {
        const res = await fetch("/api/github/repos");
        if (!res.ok) throw new Error("Failed to fetch repositories");
        const data = await res.json();
        setRepos(data);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    fetchRepos();
    fetchHistory();
    return () => {
      if (pollRef.current) clearInterval(pollRef.current);
    };
  }, []);

  async function fetchHistory() {
    // Fetch previous audits for the user
    const res = await fetch("/api/deepaudit/history");
    if (res.ok) {
      const data = await res.json();
      setHistory(data.audits || []);
    }
  }

  async function startAudit(repo: Repo) {
    setAuditLoading(repo.id);
    setAuditStarted(null);
    setError(null);
    setAuditInfo(null);
    setAuditId(null);
    setPolling(false);
    setProgress(0);
    setStatus(null);
    setReport(null);
    setChunkResults(null);
    setSelectedPanel('current');
    setSelectedHistoryAudit(null);
    try {
      const res = await fetch("/api/deepaudit/start", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          id: repo.id,
          name: repo.name,
          full_name: repo.full_name,
          owner: repo.owner,
          private: repo.private,
          html_url: repo.html_url,
          description: repo.description,
        }),
      });
      if (!res.ok) throw new Error("Failed to start DeepAudit");
      const data = await res.json();
      setAuditStarted(`Audit started for ${repo.full_name}. Audit ID: ${data.auditId || 'N/A'}`);
      setAuditId(data.auditId);
      setPolling(true);
      pollStatus(data.auditId);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setAuditLoading(null);
    }
  }

  function pollStatus(auditId: string) {
    if (pollRef.current) clearInterval(pollRef.current);
    pollRef.current = setInterval(async () => {
      const res = await fetch(`/api/deepaudit/status?id=${auditId}`);
      if (res.ok) {
        const data = await res.json();
        setStatus(data.status);
        setProgress(data.progress || 0);
        setReport(data.report);
        setChunkResults(data.chunkResults);
        if (data.status === 'completed' || data.status === 'failed') {
          setPolling(false);
          clearInterval(pollRef.current!);
          fetchHistory();
        }
      }
    }, 2000);
  }

  function viewAudit(audit: AuditJob) {
    setAuditId(audit.id);
    setPolling(false);
    setAuditStarted(`Viewing audit for ${audit.repoOwner}/${audit.repoName}`);
    setSelectedPanel('history');
    setSelectedHistoryAudit(audit);
    // Fetch the report for this audit
    fetch(`/api/deepaudit/status?id=${audit.id}`).then(async res => {
      if (res.ok) {
        const data = await res.json();
        setStatus(data.status);
        setProgress(data.progress || 0);
        setReport(data.report);
        setChunkResults(data.chunkResults);
      }
    });
  }

  // Main layout: left panel + main content
  return (
    <div
      className="flex min-h-screen"
      style={{backgroundColor: 'var(--background)'}}
    >
      {/* Enhanced Left Sidebar */}
      <aside
        className="w-80 flex flex-col"
        style={{
          backgroundColor: 'var(--card)',
          borderRight: '1px solid var(--border)'
        }}
      >
        {/* Header */}
        <div className="p-6 border-b" style={{borderColor: 'var(--border)'}}>
          <div className="flex items-center space-x-3 mb-2">
            <div
              className="p-2 rounded-lg"
              style={{backgroundColor: 'var(--primary)', color: 'var(--primary-foreground)'}}
            >
              <Zap className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-2xl font-bold" style={{color: 'var(--foreground)'}}>
                DeepAudit
              </h1>
              <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                AI-Powered Code Analysis
              </p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-6 space-y-2">
          <button
            className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left font-medium transition-all ${
              selectedPanel === 'new'
                ? 'shadow-sm'
                : 'hover:opacity-80'
            }`}
            style={{
              backgroundColor: selectedPanel === 'new' ? 'var(--primary)' : 'transparent',
              color: selectedPanel === 'new' ? 'var(--primary-foreground)' : 'var(--foreground)'
            }}
            onClick={() => { setSelectedPanel('new'); setSelectedHistoryAudit(null); }}
          >
            <Search className="h-5 w-5" />
            <span>New Audit</span>
          </button>

          <button
            className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left font-medium transition-all ${
              selectedPanel === 'current'
                ? 'shadow-sm'
                : 'hover:opacity-80'
            }`}
            style={{
              backgroundColor: selectedPanel === 'current' ? 'var(--primary)' : 'transparent',
              color: selectedPanel === 'current' ? 'var(--primary-foreground)' : 'var(--foreground)'
            }}
            onClick={() => { setSelectedPanel('current'); setSelectedHistoryAudit(null); }}
          >
            <Activity className="h-5 w-5" />
            <span>Current Audit</span>
            {polling && (
              <div className="ml-auto">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent"></div>
              </div>
            )}
          </button>

          <button
            className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left font-medium transition-all ${
              selectedPanel === 'history'
                ? 'shadow-sm'
                : 'hover:opacity-80'
            }`}
            style={{
              backgroundColor: selectedPanel === 'history' ? 'var(--primary)' : 'transparent',
              color: selectedPanel === 'history' ? 'var(--primary-foreground)' : 'var(--foreground)'
            }}
            onClick={() => { setSelectedPanel('history'); setSelectedHistoryAudit(null); }}
          >
            <Clock className="h-5 w-5" />
            <span>Previous Audits</span>
            {history.length > 0 && (
              <span
                className="ml-auto px-2 py-1 text-xs rounded-full"
                style={{
                  backgroundColor: 'var(--accent)',
                  color: 'var(--accent-foreground)'
                }}
              >
                {history.length}
              </span>
            )}
          </button>
        </nav>

        {/* Footer */}
        <div className="p-6 border-t" style={{borderColor: 'var(--border)'}}>
          <Link
            href="/dashboard"
            className="flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors hover:opacity-80"
            style={{color: 'var(--muted-foreground)'}}
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Dashboard</span>
          </Link>
        </div>
      </aside>
      {/* Enhanced Main Content */}
      <main className="flex-1 p-8 overflow-y-auto">
        {/* New Audit: Enhanced Repository Selection */}
        {selectedPanel === 'new' && (
          <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-3xl font-bold mb-2" style={{color: 'var(--foreground)'}}>
                  Select Repository to Audit
                </h2>
                <p className="text-lg" style={{color: 'var(--muted-foreground)'}}>
                  Choose a repository for comprehensive AI-powered code analysis
                </p>
              </div>
              <div
                className="px-4 py-2 rounded-lg"
                style={{
                  backgroundColor: 'var(--accent)',
                  border: '1px solid var(--border)'
                }}
              >
                <span className="text-sm font-medium" style={{color: 'var(--foreground)'}}>
                  {repos.length} repositories available
                </span>
              </div>
            </div>

            {/* Loading State */}
            {loading ? (
              <div
                className="rounded-lg p-12 text-center"
                style={{
                  backgroundColor: 'var(--card)',
                  border: '1px solid var(--border)'
                }}
              >
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4" style={{borderColor: 'var(--primary)'}}></div>
                <p className="text-lg font-medium" style={{color: 'var(--foreground)'}}>Loading repositories...</p>
                <p className="text-sm mt-2" style={{color: 'var(--muted-foreground)'}}>Fetching your GitHub repositories</p>
              </div>
            ) : error ? (
              <div
                className="rounded-lg p-6 border-l-4"
                style={{
                  backgroundColor: 'var(--card)',
                  borderLeftColor: '#ef4444',
                  border: '1px solid var(--border)'
                }}
              >
                <div className="flex items-center space-x-3">
                  <XCircle className="h-6 w-6 text-red-400" />
                  <div>
                    <h3 className="font-semibold text-red-400">Error Loading Repositories</h3>
                    <p className="text-sm mt-1" style={{color: 'var(--muted-foreground)'}}>{error}</p>
                  </div>
                </div>
              </div>
            ) : (
              /* Repository Grid */
              <div className="grid grid-cols-1 gap-4">
                {repos.map((repo) => (
                  <div
                    key={repo.id}
                    className="rounded-lg p-6 transition-all hover:shadow-lg"
                    style={{
                      backgroundColor: 'var(--card)',
                      border: '1px solid var(--border)'
                    }}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-3 mb-3">
                          <GitBranch className="h-5 w-5 text-blue-400" />
                          <h3 className="text-xl font-semibold truncate" style={{color: 'var(--foreground)'}}>
                            {repo.full_name}
                          </h3>
                          {repo.private && (
                            <span
                              className="px-2 py-1 text-xs rounded-full flex items-center space-x-1"
                              style={{
                                backgroundColor: 'var(--accent)',
                                color: 'var(--accent-foreground)'
                              }}
                            >
                              <Lock className="h-3 w-3" />
                              <span>Private</span>
                            </span>
                          )}
                        </div>

                        {repo.description && (
                          <p className="text-sm mb-4 line-clamp-2" style={{color: 'var(--muted-foreground)'}}>
                            {repo.description}
                          </p>
                        )}

                        <div className="flex items-center space-x-4 text-sm" style={{color: 'var(--muted-foreground)'}}>
                          <a
                            href={repo.html_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center space-x-1 hover:opacity-80 transition-opacity"
                            style={{color: 'var(--primary)'}}
                          >
                            <Eye className="h-4 w-4" />
                            <span>View on GitHub</span>
                          </a>
                        </div>
                      </div>

                      <button
                        className="ml-6 px-6 py-3 rounded-lg font-semibold transition-all hover:opacity-90 disabled:opacity-50 flex items-center space-x-2"
                        style={{
                          backgroundColor: 'var(--primary)',
                          color: 'var(--primary-foreground)'
                        }}
                        onClick={() => startAudit(repo)}
                        disabled={!!auditLoading}
                      >
                        {auditLoading === repo.id ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent"></div>
                            <span>Starting...</span>
                          </>
                        ) : (
                          <>
                            <Zap className="h-4 w-4" />
                            <span>Start DeepAudit</span>
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
        {/* Enhanced Current Audit Section */}
        {selectedPanel === 'current' && (
          <div className="space-y-6">
            {/* Header */}
            <div>
              <h2 className="text-3xl font-bold mb-2" style={{color: 'var(--foreground)'}}>
                Current Audit
              </h2>
              <p className="text-lg" style={{color: 'var(--muted-foreground)'}}>
                Monitor the progress of your ongoing code analysis
              </p>
            </div>

            {/* Audit Status Card */}
            {auditStarted && (
              <div
                className="rounded-lg p-6 border-l-4"
                style={{
                  backgroundColor: 'var(--card)',
                  borderLeftColor: '#10b981',
                  border: '1px solid var(--border)'
                }}
              >
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-6 w-6 text-green-400" />
                  <div>
                    <h3 className="font-semibold text-green-400">Audit Initiated</h3>
                    <p className="text-sm mt-1" style={{color: 'var(--muted-foreground)'}}>{auditStarted}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Progress Section */}
            {polling && (
              <div
                className="rounded-lg p-6"
                style={{
                  backgroundColor: 'var(--card)',
                  border: '1px solid var(--border)'
                }}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <Activity className="h-6 w-6 text-blue-400" />
                    <div>
                      <h3 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                        Analysis in Progress
                      </h3>
                      <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                        {status || 'Processing repository...'}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold" style={{color: 'var(--foreground)'}}>
                      {progress.toFixed(0)}%
                    </div>
                    <div className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                      Complete
                    </div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="w-full rounded-full h-3" style={{backgroundColor: 'var(--accent)'}}>
                  <div
                    className="h-3 rounded-full transition-all duration-500 ease-out"
                    style={{
                      width: `${progress}%`,
                      backgroundColor: 'var(--primary)'
                    }}
                  ></div>
                </div>

                {/* Progress Steps */}
                <div className="mt-6 grid grid-cols-4 gap-4">
                  {[
                    { step: 'Repository Analysis', threshold: 25, icon: Search },
                    { step: 'Code Chunking', threshold: 50, icon: Code },
                    { step: 'AI Processing', threshold: 75, icon: Zap },
                    { step: 'Report Generation', threshold: 100, icon: FileText }
                  ].map(({ step, threshold, icon: Icon }) => (
                    <div key={step} className="text-center">
                      <div
                        className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2 transition-colors ${
                          progress >= threshold ? 'text-white' : ''
                        }`}
                        style={{
                          backgroundColor: progress >= threshold ? 'var(--primary)' : 'var(--accent)',
                          color: progress >= threshold ? 'var(--primary-foreground)' : 'var(--muted-foreground)'
                        }}
                      >
                        <Icon className="h-5 w-5" />
                      </div>
                      <div className="text-xs font-medium" style={{color: 'var(--muted-foreground)'}}>
                        {step}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Completed Report */}
            {status === 'completed' && report && (
              <AuditReport report={report} chunkResults={chunkResults} openSections={openSections} setOpenSections={setOpenSections} />
            )}

            {/* Failed State */}
            {status === 'failed' && (
              <div
                className="rounded-lg p-6 border-l-4"
                style={{
                  backgroundColor: 'var(--card)',
                  borderLeftColor: '#ef4444',
                  border: '1px solid var(--border)'
                }}
              >
                <div className="flex items-center space-x-3">
                  <XCircle className="h-6 w-6 text-red-400" />
                  <div>
                    <h3 className="font-semibold text-red-400">Audit Failed</h3>
                    <p className="text-sm mt-1" style={{color: 'var(--muted-foreground)'}}>
                      The audit process encountered an error. Please try again later.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* No Audit State */}
            {!polling && !report && !auditStarted && (
              <div
                className="rounded-lg p-12 text-center"
                style={{
                  backgroundColor: 'var(--card)',
                  border: '1px solid var(--border)'
                }}
              >
                <Activity className="h-16 w-16 mx-auto mb-4 opacity-50" style={{color: 'var(--muted-foreground)'}} />
                <h3 className="text-xl font-semibold mb-2" style={{color: 'var(--foreground)'}}>
                  No Active Audit
                </h3>
                <p className="text-lg mb-6" style={{color: 'var(--muted-foreground)'}}>
                  Start a new audit to see the analysis progress here
                </p>
                <button
                  className="px-6 py-3 rounded-lg font-semibold transition-colors hover:opacity-90"
                  style={{
                    backgroundColor: 'var(--primary)',
                    color: 'var(--primary-foreground)'
                  }}
                  onClick={() => setSelectedPanel('new')}
                >
                  Start New Audit
                </button>
              </div>
            )}
          </div>
        )}
        {/* Enhanced Previous Audits Section */}
        {selectedPanel === 'history' && (
          <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-3xl font-bold mb-2" style={{color: 'var(--foreground)'}}>
                  Previous Audits
                </h2>
                <p className="text-lg" style={{color: 'var(--muted-foreground)'}}>
                  Review and analyze your completed audit reports
                </p>
              </div>
              {history.length > 0 && (
                <div
                  className="px-4 py-2 rounded-lg"
                  style={{
                    backgroundColor: 'var(--accent)',
                    border: '1px solid var(--border)'
                  }}
                >
                  <span className="text-sm font-medium" style={{color: 'var(--foreground)'}}>
                    {history.length} audit{history.length !== 1 ? 's' : ''} completed
                  </span>
                </div>
              )}
            </div>

            {/* Empty State */}
            {history.length === 0 ? (
              <div
                className="rounded-lg p-12 text-center"
                style={{
                  backgroundColor: 'var(--card)',
                  border: '1px solid var(--border)'
                }}
              >
                <Clock className="h-16 w-16 mx-auto mb-4 opacity-50" style={{color: 'var(--muted-foreground)'}} />
                <h3 className="text-xl font-semibold mb-2" style={{color: 'var(--foreground)'}}>
                  No Previous Audits
                </h3>
                <p className="text-lg mb-6" style={{color: 'var(--muted-foreground)'}}>
                  Your completed audit reports will appear here
                </p>
                <button
                  className="px-6 py-3 rounded-lg font-semibold transition-colors hover:opacity-90"
                  style={{
                    backgroundColor: 'var(--primary)',
                    color: 'var(--primary-foreground)'
                  }}
                  onClick={() => setSelectedPanel('new')}
                >
                  Start Your First Audit
                </button>
              </div>
            ) : (
              <>
                {/* Audit History List */}
                <div className="grid grid-cols-1 gap-4 mb-8">
                  {history.map(audit => (
                    <div
                      key={audit.id}
                      className={`rounded-lg p-6 cursor-pointer transition-all hover:shadow-lg ${
                        selectedHistoryAudit?.id === audit.id ? 'ring-2 ring-blue-500' : ''
                      }`}
                      style={{
                        backgroundColor: 'var(--card)',
                        border: '1px solid var(--border)'
                      }}
                      onClick={() => viewAudit(audit)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-3 mb-3">
                            <GitBranch className="h-5 w-5 text-blue-400" />
                            <h3 className="text-xl font-semibold truncate" style={{color: 'var(--foreground)'}}>
                              {audit.repoOwner}/{audit.repoName}
                            </h3>
                            <span
                              className={`px-3 py-1 text-xs rounded-full font-medium ${
                                audit.status === 'completed'
                                  ? 'bg-green-100 text-green-700'
                                  : audit.status === 'failed'
                                  ? 'bg-red-100 text-red-700'
                                  : 'bg-yellow-100 text-yellow-700'
                              }`}
                            >
                              {audit.status === 'completed' && <CheckCircle className="inline h-3 w-3 mr-1" />}
                              {audit.status === 'failed' && <XCircle className="inline h-3 w-3 mr-1" />}
                              {audit.status === 'pending' && <Clock className="inline h-3 w-3 mr-1" />}
                              {audit.status.charAt(0).toUpperCase() + audit.status.slice(1)}
                            </span>
                          </div>

                          <div className="grid grid-cols-2 gap-4 text-sm" style={{color: 'var(--muted-foreground)'}}>
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4" />
                              <span>Started: {new Date(audit.startedAt).toLocaleDateString()}</span>
                            </div>
                            {audit.completedAt && (
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="h-4 w-4" />
                                <span>Completed: {new Date(audit.completedAt).toLocaleDateString()}</span>
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="ml-6 flex items-center space-x-2">
                          <button
                            className="p-2 rounded-lg transition-colors hover:opacity-80"
                            style={{
                              backgroundColor: 'var(--accent)',
                              color: 'var(--accent-foreground)'
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              viewAudit(audit);
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Selected Audit Report */}
                {selectedHistoryAudit && report && (
                  <AuditReport report={report} chunkResults={chunkResults} openSections={openSections} setOpenSections={setOpenSections} />
                )}

                {/* No Selection State */}
                {!selectedHistoryAudit && (
                  <div
                    className="rounded-lg p-8 text-center"
                    style={{
                      backgroundColor: 'var(--card)',
                      border: '1px solid var(--border)'
                    }}
                  >
                    <FileText className="h-12 w-12 mx-auto mb-3 opacity-50" style={{color: 'var(--muted-foreground)'}} />
                    <p className="text-lg" style={{color: 'var(--muted-foreground)'}}>
                      Select an audit from the list above to view its detailed report
                    </p>
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </main>
    </div>
  );
}

// Enhanced AuditReport component with modern dark theme design
function AuditReport({ report, chunkResults, openSections, setOpenSections }: any) {
  const sectionNames = report && report.sections && typeof report.sections === 'object' ? Object.keys(report.sections) : [];

  return (
    <div className="space-y-6">
      {/* Enhanced Summary Card */}
      <div
        className="rounded-lg p-8"
        style={{
          background: 'linear-gradient(135deg, var(--primary) 0%, #3b82f6 100%)',
          color: 'var(--primary-foreground)'
        }}
      >
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 rounded-lg bg-white/20">
              <FileText className="h-8 w-8" />
            </div>
            <div>
              <h2 className="text-3xl font-bold mb-1">DeepAudit Report</h2>
              <p className="text-lg opacity-90">{report.summary}</p>
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              className="p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors"
              title="Download Report"
            >
              <Download className="h-5 w-5" />
            </button>
            <button
              className="p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors"
              title="Share Report"
            >
              <Share2 className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold mb-1">{report.totalFiles}</div>
            <div className="text-sm opacity-80">Files Analyzed</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold mb-1">{report.totalChunks}</div>
            <div className="text-sm opacity-80">Code Chunks</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold mb-1">{sectionNames.length}</div>
            <div className="text-sm opacity-80">Report Sections</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold mb-1">
              {Array.isArray(report.criticalIssues) ? report.criticalIssues.length : 0}
            </div>
            <div className="text-sm opacity-80">Critical Issues</div>
          </div>
        </div>
      </div>
      {/* Enhanced Critical Issues */}
      {Array.isArray(report.criticalIssues) && report.criticalIssues.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center space-x-3 mb-6">
            <AlertTriangle className="h-8 w-8 text-red-400" />
            <h3 className="text-2xl font-bold text-red-400">Critical Issues Found</h3>
          </div>

          {report.criticalIssues.map((issue: string, idx: number) => (
            <div
              key={idx}
              className="rounded-lg p-6 border-l-4"
              style={{
                backgroundColor: 'var(--card)',
                borderLeftColor: '#ef4444',
                border: '1px solid var(--border)'
              }}
            >
              <div className="flex items-start space-x-4">
                <div className="p-2 rounded-lg bg-red-100">
                  <Bug className="h-6 w-6 text-red-600" />
                </div>
                <div className="flex-1">
                  <h4 className="font-bold text-lg text-red-400 mb-3">
                    Critical Issue #{idx + 1}
                  </h4>
                  <div
                    className="prose max-w-none whitespace-pre-line text-sm leading-relaxed"
                    style={{color: 'var(--foreground)'}}
                  >
                    {issue}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      {/* Enhanced Report Sections */}
      {report.sections && typeof report.sections === 'object' ? (
        <div className="space-y-6">
          <div className="flex items-center space-x-3 mb-6">
            <BarChart3 className="h-8 w-8" style={{color: 'var(--primary)'}} />
            <h3 className="text-2xl font-bold" style={{color: 'var(--foreground)'}}>
              Detailed Analysis Report
            </h3>
          </div>

          <div className="grid grid-cols-1 gap-6">
            {Object.entries(report.sections).map(([section, content], idx) => {
              // Enhanced section icons and colors
              const sectionConfig: Record<string, { icon: any, color: string, bgColor: string }> = {
                'Executive Summary': { icon: TrendingUp, color: '#0891b2', bgColor: 'rgba(8, 145, 178, 0.1)' },
                'Code Quality Assessment': { icon: Code, color: '#3b82f6', bgColor: 'rgba(59, 130, 246, 0.1)' },
                'Security Analysis': { icon: Shield, color: '#ef4444', bgColor: 'rgba(239, 68, 68, 0.1)' },
                'Performance Evaluation': { icon: Zap, color: '#f59e0b', bgColor: 'rgba(245, 158, 11, 0.1)' },
                'Testing & Coverage': { icon: CheckCircle, color: '#10b981', bgColor: 'rgba(16, 185, 129, 0.1)' },
                'Documentation & Comments': { icon: FileText, color: '#8b5cf6', bgColor: 'rgba(139, 92, 246, 0.1)' },
                'Dependency Analysis': { icon: GitBranch, color: '#ec4899', bgColor: 'rgba(236, 72, 153, 0.1)' },
                'Architecture & Design': { icon: BarChart3, color: '#6366f1', bgColor: 'rgba(99, 102, 241, 0.1)' },
                'Recommendations': { icon: Lightbulb, color: '#f97316', bgColor: 'rgba(249, 115, 22, 0.1)' },
              };

              const config = sectionConfig[section] || { icon: FileText, color: '#6b7280', bgColor: 'rgba(107, 114, 128, 0.1)' };
              const Icon = config.icon;
              const open = openSections[section] ?? true;

              return (
                <div
                  key={section}
                  className="rounded-lg border-l-4 transition-all"
                  style={{
                    backgroundColor: 'var(--card)',
                    borderLeftColor: config.color,
                    border: '1px solid var(--border)'
                  }}
                >
                  <div
                    className="flex items-center justify-between p-6 cursor-pointer hover:opacity-80 transition-opacity"
                    onClick={() => setOpenSections((prev: any) => ({ ...prev, [section]: !prev[section] }))}
                  >
                    <div className="flex items-center space-x-4">
                      <div
                        className="p-3 rounded-lg"
                        style={{backgroundColor: config.bgColor}}
                      >
                        <Icon className="h-6 w-6" style={{color: config.color}} />
                      </div>
                      <div>
                        <h4 className="text-xl font-bold" style={{color: 'var(--foreground)'}}>
                          {section}
                        </h4>
                        <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                          Click to {open ? 'collapse' : 'expand'} section
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {open ? (
                        <ChevronDown className="h-5 w-5" style={{color: 'var(--muted-foreground)'}} />
                      ) : (
                        <ChevronRight className="h-5 w-5" style={{color: 'var(--muted-foreground)'}} />
                      )}
                    </div>
                  </div>

                  {open && (
                    <div className="px-6 pb-6">
                      <div
                        className="prose max-w-none whitespace-pre-line text-sm leading-relaxed p-4 rounded-lg"
                        style={{
                          backgroundColor: 'var(--accent)',
                          color: 'var(--foreground)',
                          border: '1px solid var(--border)'
                        }}
                      >
                        {String(content)}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      ) : (
        <div
          className="rounded-lg p-6"
          style={{
            backgroundColor: 'var(--card)',
            border: '1px solid var(--border)'
          }}
        >
          <div
            className="whitespace-pre-wrap text-sm leading-relaxed p-4 rounded-lg"
            style={{
              backgroundColor: 'var(--accent)',
              color: 'var(--foreground)'
            }}
          >
            {report.findings}
          </div>
        </div>
      )}
      {/* Enhanced Chunk Results */}
      {chunkResults && (
        <div className="space-y-6">
          <div className="flex items-center space-x-3">
            <Code className="h-8 w-8" style={{color: 'var(--primary)'}} />
            <h3 className="text-2xl font-bold" style={{color: 'var(--foreground)'}}>
              AI Analysis Results by Chunk
            </h3>
          </div>

          <div className="grid grid-cols-1 gap-4">
            {chunkResults.map((result: any, idx: number) => (
              <div
                key={idx}
                className="rounded-lg p-6"
                style={{
                  backgroundColor: 'var(--card)',
                  border: '1px solid var(--border)'
                }}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div
                      className="p-2 rounded-lg"
                      style={{backgroundColor: 'var(--accent)'}}
                    >
                      <Code className="h-5 w-5" style={{color: 'var(--primary)'}} />
                    </div>
                    <div>
                      <h4 className="font-bold text-lg" style={{color: 'var(--foreground)'}}>
                        Chunk {result.chunkIndex + 1}
                      </h4>
                      <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                        {result.fileCount} files • {result.totalTokens} tokens
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <span
                      className="px-3 py-1 text-xs rounded-full font-medium"
                      style={{
                        backgroundColor: 'var(--accent)',
                        color: 'var(--accent-foreground)'
                      }}
                    >
                      Analyzed
                    </span>
                  </div>
                </div>

                <div
                  className="whitespace-pre-wrap text-sm leading-relaxed p-4 rounded-lg font-mono"
                  style={{
                    backgroundColor: 'var(--accent)',
                    color: 'var(--foreground)',
                    border: '1px solid var(--border)'
                  }}
                >
                  {result.summary}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}