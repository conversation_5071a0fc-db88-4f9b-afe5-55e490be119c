'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';

interface PR {
  id: number;
  number: number;
  title: string;
  state: string;
  html_url: string;
  repo: string;
  owner: string;
  user: string;
  head: { ref: string };
  base: { ref: string };
}

export default function PRDashboardPage() {
  const [prs, setPRs] = useState<PR[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchPRs() {
      setLoading(true);
      setError(null);
      try {
        const res = await fetch('/api/github/pull-requests');
        if (!res.ok) throw new Error('Failed to fetch pull requests');
        const data = await res.json();
        setPRs(data);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    fetchPRs();
  }, []);

  return (
    <div className="p-8 max-w-5xl mx-auto">
      <h2 className="text-3xl font-bold mb-6 text-white">Open Pull Requests (Monitored Repos)</h2>
      {loading ? (
        <div>Loading pull requests...</div>
      ) : error ? (
        <div className="text-red-600">{error}</div>
      ) : prs.length === 0 ? (
        <div className="text-gray-400">No open pull requests found for your monitored repositories.</div>
      ) : (
        <div className="bg-white rounded-lg shadow p-6">
          <table className="w-full text-left">
            <thead>
              <tr className="border-b">
                <th className="py-2 px-2">Title</th>
                <th className="py-2 px-2">Author</th>
                <th className="py-2 px-2">Branch</th>
                <th className="py-2 px-2">Status</th>
                <th className="py-2 px-2">Actions</th>
              </tr>
            </thead>
            <tbody>
              {prs.map(pr => (
                <tr key={pr.id} className="border-b hover:bg-gray-50">
                  <td className="py-2 px-2">
                    <a href={pr.html_url} target="_blank" rel="noopener noreferrer" className="text-blue-700 hover:underline font-semibold">
                      {pr.title}
                    </a>
                    <div className="text-xs text-gray-500">{pr.owner}/{pr.repo} #{pr.number}</div>
                  </td>
                  <td className="py-2 px-2">{pr.user}</td>
                  <td className="py-2 px-2">
                    <span className="font-mono bg-gray-100 px-2 py-1 rounded text-xs">{pr.head?.ref}</span>
                  </td>
                  <td className="py-2 px-2">
                    <span className={`px-2 py-1 rounded text-xs font-semibold ${pr.state === 'open' ? 'bg-green-100 text-green-700' : 'bg-gray-200 text-gray-700'}`}>{pr.state}</span>
                  </td>
                  <td className="py-2 px-2">
                    <Link href={`/dashboard/prs/${pr.owner}/${pr.repo}/${pr.number}`} className="bg-blue-600 text-white px-4 py-1 rounded hover:bg-blue-700 text-sm font-semibold">Review</Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
} 