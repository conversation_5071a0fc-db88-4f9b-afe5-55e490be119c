import { notFound } from 'next/navigation';

async function getReview(id: string) {
  const res = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/share/code-quality-review/fetch?id=${id}`);
  if (!res.ok) return null;
  const data = await res.json();
  return data.review || null;
}

type Props = {
  params: Promise<{ id: string }>;
};

export default async function ShareCodeQualityReviewPage({
  params,
}: Props) {
  const resolvedParams = await params;
  const review = await getReview(resolvedParams.id);
  if (!review) return notFound();
  return (
    <div className="max-w-2xl mx-auto p-8">
      <h1 className="text-3xl font-bold mb-6">Code Quality AI Review (Shared)</h1>
      <div className="space-y-4">
        {review.map((item: any, idx: number) => (
          <div
            key={idx}
            className={`rounded-lg p-4 shadow flex flex-col gap-2 ${
              item.type === 'security'
                ? 'bg-red-100 border-l-8 border-red-500'
                : item.type === 'performance'
                ? 'bg-yellow-100 border-l-8 border-yellow-500'
                : item.type === 'quality' && item.level === 'success'
                ? 'bg-green-100 border-l-8 border-green-500'
                : 'bg-blue-100 border-l-8 border-blue-500'
            }`}
          >
            <div className="flex items-center gap-2">
              {item.type === 'security' && <span className="text-red-600 text-xl">🚨</span>}
              {item.type === 'performance' && <span className="text-yellow-600 text-xl">⚡</span>}
              {item.type === 'quality' && item.level === 'success' && <span className="text-green-600 text-xl">✨</span>}
              {item.type === 'tip' && <span className="text-blue-600 text-xl">💡</span>}
              <span className="font-bold text-lg text-gray-900">{item.title}</span>
            </div>
            <div className="text-gray-800 whitespace-pre-line">{item.message}</div>
            {item.code && (
              <pre className="bg-gray-900 text-green-200 rounded p-2 overflow-x-auto text-xs mt-2">{item.code}</pre>
            )}
            {item.line && (
              <div className="text-xs text-gray-500">Line: {item.line}</div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
} 