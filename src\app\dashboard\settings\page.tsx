'use client';

import { useSession } from 'next-auth/react';
import { useState, useEffect } from 'react';
import {
  Settings,
  Save,
  RefreshCw,
  Check,
  Sliders,
  Shield,
  Zap,
  Code,
  Wrench,
  Bell,
  MessageSquare,
  Eye,
  ToggleLeft,
  ToggleRight,
  Info,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

interface ReviewSettings {
  codeStyleWeight: number;
  securityWeight: number;
  performanceWeight: number;
  maintainabilityWeight: number;
  enableInlineComments: boolean;
  enableSummaryComments: boolean;
  autoPostReviews: boolean;
  notifyOnReview: boolean;
}

export default function SettingsPage() {
  const { data: session, status } = useSession();
  const [settings, setSettings] = useState<ReviewSettings>({
    codeStyleWeight: 50,
    securityWeight: 50,
    performanceWeight: 50,
    maintainabilityWeight: 50,
    enableInlineComments: true,
    enableSummaryComments: true,
    autoPostReviews: false,
    notifyOnReview: true,
  });
  const [saving, setSaving] = useState(false);
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('review-focus');

  useEffect(() => {
    async function fetchSettings() {
      setLoading(true);
      setError(null);
      try {
        const res = await fetch('/api/settings/review');
        if (res.ok) {
          const data = await res.json();
          setSettings(data);
        } else {
          throw new Error('Failed to fetch settings');
        }
      } catch (error: any) {
        setError(error.message);
        console.error('Failed to fetch settings:', error);
      } finally {
        setLoading(false);
      }
    }
    if (session?.user) {
      fetchSettings();
    } else if (status !== 'loading') {
      setLoading(false);
    }
  }, [session, status]);

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setSuccess(false);
    setError(null);
    try {
      const res = await fetch('/api/settings/review', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings),
      });
      if (!res.ok) throw new Error('Failed to save settings');
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (error: any) {
      setError(error.message);
      console.error('Failed to save settings:', error);
    } finally {
      setSaving(false);
    }
  };

  const resetToDefaults = () => {
    setSettings({
      codeStyleWeight: 50,
      securityWeight: 50,
      performanceWeight: 50,
      maintainabilityWeight: 50,
      enableInlineComments: true,
      enableSummaryComments: true,
      autoPostReviews: false,
      notifyOnReview: true,
    });
  };

  const tabs = [
    { id: 'review-focus', label: 'Review Focus', icon: Sliders },
    { id: 'review-options', label: 'Review Options', icon: Settings },
    { id: 'notifications', label: 'Notifications', icon: Bell },
  ];

  if (status === 'loading' || loading) {
    return (
      <main
        className="p-6"
        style={{backgroundColor: 'var(--background)', minHeight: '100vh'}}
      >
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style={{borderColor: 'var(--primary)'}}></div>
            <p style={{color: 'var(--muted-foreground)'}}>Loading settings...</p>
          </div>
        </div>
      </main>
    );
  }

  if (!session) {
    return (
      <main
        className="p-6"
        style={{backgroundColor: 'var(--background)', minHeight: '100vh'}}
      >
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-red-400" />
            <p className="text-lg font-medium mb-2" style={{color: 'var(--foreground)'}}>Authentication Required</p>
            <p style={{color: 'var(--muted-foreground)'}}>Please sign in to access settings.</p>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main
      className="p-6"
      style={{backgroundColor: 'var(--background)', minHeight: '100vh'}}
    >
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <header className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-3">
            <Settings className="h-8 w-8" style={{color: 'var(--primary)'}} />
            <div>
              <h1 className="text-3xl font-bold" style={{color: 'var(--foreground)'}}>
                Review Settings
              </h1>
              <p className="text-sm mt-1" style={{color: 'var(--muted-foreground)'}}>
                Configure AI review preferences and behavior
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={resetToDefaults}
              className="flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors hover:opacity-80"
              style={{
                backgroundColor: 'var(--secondary)',
                color: 'var(--secondary-foreground)'
              }}
            >
              <RefreshCw className="h-4 w-4" />
              <span>Reset to Defaults</span>
            </button>
          </div>
        </header>

        {/* Tabs */}
        <div className="flex space-x-1 mb-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  isActive ? 'font-medium' : ''
                }`}
                style={{
                  backgroundColor: isActive ? 'var(--primary)' : 'var(--secondary)',
                  color: isActive ? 'var(--primary-foreground)' : 'var(--secondary-foreground)'
                }}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>

        <form onSubmit={handleSave} className="space-y-8">
          {/* Review Focus Areas Tab */}
          {activeTab === 'review-focus' && (
            <section
              className="rounded-lg p-6"
              style={{
                backgroundColor: 'var(--card)',
                border: '1px solid var(--border)'
              }}
            >
              <div className="flex items-center space-x-2 mb-6">
                <Sliders className="h-5 w-5" style={{color: 'var(--primary)'}} />
                <h2 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                  Review Focus Areas
                </h2>
              </div>

              <div className="space-y-6">
                {[
                  { key: 'codeStyleWeight', label: 'Code Style', icon: Code, color: 'text-blue-400' },
                  { key: 'securityWeight', label: 'Security', icon: Shield, color: 'text-red-400' },
                  { key: 'performanceWeight', label: 'Performance', icon: Zap, color: 'text-yellow-400' },
                  { key: 'maintainabilityWeight', label: 'Maintainability', icon: Wrench, color: 'text-green-400' }
                ].map(({ key, label, icon: Icon, color }) => (
                  <div key={key} className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Icon className={`h-5 w-5 ${color}`} />
                        <label className="font-medium" style={{color: 'var(--foreground)'}}>
                          {label}
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-mono px-2 py-1 rounded" style={{
                          backgroundColor: 'var(--accent)',
                          color: 'var(--accent-foreground)'
                        }}>
                          {settings[key as keyof ReviewSettings]}%
                        </span>
                      </div>
                    </div>
                    <div className="relative">
                      <input
                        type="range"
                        min="0"
                        max="100"
                        value={settings[key as keyof ReviewSettings] as number}
                        onChange={e => setSettings({ ...settings, [key]: parseInt(e.target.value) })}
                        className="w-full h-2 rounded-lg appearance-none cursor-pointer"
                        style={{
                          background: `linear-gradient(to right, var(--primary) 0%, var(--primary) ${settings[key as keyof ReviewSettings]}%, var(--secondary) ${settings[key as keyof ReviewSettings]}%, var(--secondary) 100%)`
                        }}
                      />
                    </div>
                    <div className="flex justify-between text-xs" style={{color: 'var(--muted-foreground)'}}>
                      <span>Low Priority</span>
                      <span>High Priority</span>
                    </div>
                  </div>
                ))}
              </div>

              <div
                className="mt-6 p-4 rounded-lg"
                style={{
                  backgroundColor: 'var(--accent)',
                  border: '1px solid var(--border)'
                }}
              >
                <div className="flex items-start space-x-3">
                  <Info className="h-5 w-5 mt-0.5" style={{color: 'var(--primary)'}} />
                  <div>
                    <h4 className="font-medium mb-1" style={{color: 'var(--foreground)'}}>
                      Focus Area Weights
                    </h4>
                    <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                      These weights determine how much attention the AI reviewer pays to each area.
                      Higher values mean more detailed analysis and feedback in that category.
                    </p>
                  </div>
                </div>
              </div>
            </section>
          )}

          {/* Review Options Tab */}
          {activeTab === 'review-options' && (
            <section
              className="rounded-lg p-6"
              style={{
                backgroundColor: 'var(--card)',
                border: '1px solid var(--border)'
              }}
            >
              <div className="flex items-center space-x-2 mb-6">
                <Settings className="h-5 w-5" style={{color: 'var(--primary)'}} />
                <h2 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                  Review Options
                </h2>
              </div>

              <div className="space-y-6">
                {[
                  {
                    key: 'enableInlineComments',
                    label: 'Enable Inline Comments',
                    description: 'Add specific comments directly on code lines',
                    icon: MessageSquare
                  },
                  {
                    key: 'enableSummaryComments',
                    label: 'Enable Summary Comments',
                    description: 'Provide overall review summaries',
                    icon: Eye
                  },
                  {
                    key: 'autoPostReviews',
                    label: 'Automatically Post Reviews',
                    description: 'Post AI reviews immediately without manual approval',
                    icon: Zap
                  }
                ].map(({ key, label, description, icon: Icon }) => (
                  <div
                    key={key}
                    className="flex items-start space-x-4 p-4 rounded-lg border"
                    style={{
                      backgroundColor: 'var(--background)',
                      borderColor: 'var(--border)'
                    }}
                  >
                    <Icon className="h-5 w-5 mt-1" style={{color: 'var(--primary)'}} />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium" style={{color: 'var(--foreground)'}}>
                            {label}
                          </h4>
                          <p className="text-sm mt-1" style={{color: 'var(--muted-foreground)'}}>
                            {description}
                          </p>
                        </div>
                        <button
                          type="button"
                          onClick={() => setSettings({ ...settings, [key]: !settings[key as keyof ReviewSettings] })}
                          className="ml-4"
                        >
                          {settings[key as keyof ReviewSettings] ? (
                            <ToggleRight className="h-6 w-6" style={{color: 'var(--primary)'}} />
                          ) : (
                            <ToggleLeft className="h-6 w-6" style={{color: 'var(--muted-foreground)'}} />
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Notifications Tab */}
          {activeTab === 'notifications' && (
            <section
              className="rounded-lg p-6"
              style={{
                backgroundColor: 'var(--card)',
                border: '1px solid var(--border)'
              }}
            >
              <div className="flex items-center space-x-2 mb-6">
                <Bell className="h-5 w-5" style={{color: 'var(--primary)'}} />
                <h2 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                  Notification Preferences
                </h2>
              </div>

              <div className="space-y-6">
                <div
                  className="flex items-start space-x-4 p-4 rounded-lg border"
                  style={{
                    backgroundColor: 'var(--background)',
                    borderColor: 'var(--border)'
                  }}
                >
                  <Bell className="h-5 w-5 mt-1" style={{color: 'var(--primary)'}} />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium" style={{color: 'var(--foreground)'}}>
                          Review Notifications
                        </h4>
                        <p className="text-sm mt-1" style={{color: 'var(--muted-foreground)'}}>
                          Get notified when AI reviews are completed and posted
                        </p>
                      </div>
                      <button
                        type="button"
                        onClick={() => setSettings({ ...settings, notifyOnReview: !settings.notifyOnReview })}
                        className="ml-4"
                      >
                        {settings.notifyOnReview ? (
                          <ToggleRight className="h-6 w-6" style={{color: 'var(--primary)'}} />
                        ) : (
                          <ToggleLeft className="h-6 w-6" style={{color: 'var(--muted-foreground)'}} />
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          )}

          {/* Save Section */}
          <div className="flex items-center justify-between pt-6 border-t" style={{borderColor: 'var(--border)'}}>
            <div className="flex items-center space-x-4">
              {error && (
                <div className="flex items-center space-x-2 text-red-400">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="text-sm">{error}</span>
                </div>
              )}
              {success && (
                <div className="flex items-center space-x-2 text-green-400">
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">Settings saved successfully!</span>
                </div>
              )}
            </div>
            <button
              type="submit"
              disabled={saving}
              className="flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-all hover:opacity-90 disabled:opacity-50"
              style={{
                backgroundColor: 'var(--primary)',
                color: 'var(--primary-foreground)'
              }}
            >
              {saving ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  <span>Save Settings</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </main>
  );
}