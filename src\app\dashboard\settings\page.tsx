'use client';

import { useSession } from 'next-auth/react';
import { useState, useEffect } from 'react';

interface ReviewSettings {
  codeStyleWeight: number;
  securityWeight: number;
  performanceWeight: number;
  maintainabilityWeight: number;
  enableInlineComments: boolean;
  enableSummaryComments: boolean;
  autoPostReviews: boolean;
  notifyOnReview: boolean;
}

export default function SettingsPage() {
  const { data: session, status } = useSession();
  const [settings, setSettings] = useState<ReviewSettings>({
    codeStyleWeight: 50,
    securityWeight: 50,
    performanceWeight: 50,
    maintainabilityWeight: 50,
    enableInlineComments: true,
    enableSummaryComments: true,
    autoPostReviews: false,
    notifyOnReview: true,
  });
  const [saving, setSaving] = useState(false);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    async function fetchSettings() {
      try {
        const res = await fetch('/api/settings/review');
        if (res.ok) {
          const data = await res.json();
          setSettings(data);
        }
      } catch (error) {
        console.error('Failed to fetch settings:', error);
      }
    }
    if (session?.user) {
      fetchSettings();
    }
  }, [session]);

  if (status === 'loading') {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }

  if (!session) {
    return <div className="min-h-screen flex items-center justify-center text-red-600">Not signed in.</div>;
  }

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setSuccess(false);
    try {
      const res = await fetch('/api/settings/review', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings),
      });
      if (!res.ok) throw new Error('Failed to save settings');
      setSuccess(true);
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="p-8">
      <h2 className="text-3xl font-bold mb-4 text-white">Review Settings</h2>
      <div className="bg-white rounded-lg shadow p-6 max-w-2xl">
        <form onSubmit={handleSave} className="space-y-6">
          {/* Review Focus Areas */}
          <div>
            <h3 className="text-xl font-semibold mb-4">Review Focus Areas</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Code Style ({settings.codeStyleWeight}%)
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={settings.codeStyleWeight}
                  onChange={e => setSettings({ ...settings, codeStyleWeight: parseInt(e.target.value) })}
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Security ({settings.securityWeight}%)
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={settings.securityWeight}
                  onChange={e => setSettings({ ...settings, securityWeight: parseInt(e.target.value) })}
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Performance ({settings.performanceWeight}%)
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={settings.performanceWeight}
                  onChange={e => setSettings({ ...settings, performanceWeight: parseInt(e.target.value) })}
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Maintainability ({settings.maintainabilityWeight}%)
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={settings.maintainabilityWeight}
                  onChange={e => setSettings({ ...settings, maintainabilityWeight: parseInt(e.target.value) })}
                  className="w-full"
                />
              </div>
            </div>
          </div>

          {/* Review Options */}
          <div>
            <h3 className="text-xl font-semibold mb-4">Review Options</h3>
            <div className="space-y-4">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={settings.enableInlineComments}
                  onChange={e => setSettings({ ...settings, enableInlineComments: e.target.checked })}
                  className="accent-blue-600"
                />
                <span>Enable inline comments</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={settings.enableSummaryComments}
                  onChange={e => setSettings({ ...settings, enableSummaryComments: e.target.checked })}
                  className="accent-blue-600"
                />
                <span>Enable summary comments</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={settings.autoPostReviews}
                  onChange={e => setSettings({ ...settings, autoPostReviews: e.target.checked })}
                  className="accent-blue-600"
                />
                <span>Automatically post reviews</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={settings.notifyOnReview}
                  onChange={e => setSettings({ ...settings, notifyOnReview: e.target.checked })}
                  className="accent-blue-600"
                />
                <span>Notify me when reviews are posted</span>
              </label>
            </div>
          </div>

          <button
            type="submit"
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
            disabled={saving}
          >
            {saving ? 'Saving...' : 'Save Settings'}
          </button>
          {success && <div className="text-green-600">Settings saved!</div>}
        </form>
      </div>
    </div>
  );
} 