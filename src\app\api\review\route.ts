import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const { searchParams } = new URL(req.url);
  const repositoryId = searchParams.get('repositoryId');
  const pullRequestId = searchParams.get('pullRequestId');
  if (!repositoryId || !pullRequestId) {
    return NextResponse.json({ error: 'Missing parameters' }, { status: 400 });
  }
  const review = await prisma.pullRequestReview.findUnique({
    where: { pullRequestId_repositoryId: { pullRequestId, repositoryId } },
  });
  if (!review) return NextResponse.json({});
  return NextResponse.json({ id: review.id, review: review.review });
}

export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const { pullRequestId, repositoryId, review } = await req.json();
  if (!pullRequestId || !repositoryId || !review) {
    return NextResponse.json({ error: 'Missing fields' }, { status: 400 });
  }
  const saved = await prisma.pullRequestReview.upsert({
    where: { pullRequestId_repositoryId: { pullRequestId, repositoryId } },
    update: { review },
    create: { pullRequestId, repositoryId, review },
  });
  return NextResponse.json({ id: saved.id, review: saved.review });
} 