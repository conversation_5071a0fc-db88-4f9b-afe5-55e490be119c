/*
  Warnings:

  - You are about to drop the column `githubRepoId` on the `MonitoredRepository` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `MonitoredRepository` table. All the data in the column will be lost.
  - You are about to drop the column `owner` on the `MonitoredRepository` table. All the data in the column will be lost.
  - You are about to drop the column `githubId` on the `User` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[userId,repoId]` on the table `MonitoredRepository` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "MonitoredRepository" DROP CONSTRAINT "MonitoredRepository_userId_fkey";

-- DropIndex
DROP INDEX "User_githubId_key";

-- AlterTable
ALTER TABLE "MonitoredRepository" DROP COLUMN "githubRepoId",
DROP COLUMN "name",
DROP COLUMN "owner",
ADD COLUMN     "repoId" TEXT,
ADD COLUMN     "repoName" TEXT,
ADD COLUMN     "repoOwner" TEXT,
ADD COLUMN     "updatedAt" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "User" DROP COLUMN "githubId",
ADD COLUMN     "emailVerified" TIMESTAMP(3);

-- CreateTable
CREATE TABLE "Account" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,

    CONSTRAINT "Account_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Session" (
    "id" TEXT NOT NULL,
    "sessionToken" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Session_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VerificationToken" (
    "identifier" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL
);

-- CreateTable
CREATE TABLE "ReviewStats" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "repositoryId" TEXT NOT NULL,
    "totalReviews" INTEGER NOT NULL DEFAULT 0,
    "bugsFound" INTEGER NOT NULL DEFAULT 0,
    "securityIssues" INTEGER NOT NULL DEFAULT 0,
    "performanceIssues" INTEGER NOT NULL DEFAULT 0,
    "maintainabilityIssues" INTEGER NOT NULL DEFAULT 0,
    "averageReviewTime" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReviewStats_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ReviewSettings" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "codeStyleWeight" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "securityWeight" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "performanceWeight" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "maintainabilityWeight" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "enableInlineComments" BOOLEAN NOT NULL DEFAULT true,
    "enableSummaryComments" BOOLEAN NOT NULL DEFAULT true,
    "enableBugDetection" BOOLEAN NOT NULL DEFAULT true,
    "enableSecurityChecks" BOOLEAN NOT NULL DEFAULT true,
    "enablePerformanceChecks" BOOLEAN NOT NULL DEFAULT true,
    "enableMaintainabilityChecks" BOOLEAN NOT NULL DEFAULT true,
    "enableNotifications" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReviewSettings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Account_provider_providerAccountId_key" ON "Account"("provider", "providerAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "Session_sessionToken_key" ON "Session"("sessionToken");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_token_key" ON "VerificationToken"("token");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_identifier_token_key" ON "VerificationToken"("identifier", "token");

-- CreateIndex
CREATE INDEX "ReviewStats_userId_idx" ON "ReviewStats"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "ReviewStats_userId_repositoryId_key" ON "ReviewStats"("userId", "repositoryId");

-- CreateIndex
CREATE UNIQUE INDEX "ReviewSettings_userId_key" ON "ReviewSettings"("userId");

-- CreateIndex
CREATE INDEX "ReviewSettings_userId_idx" ON "ReviewSettings"("userId");

-- CreateIndex
CREATE INDEX "MonitoredRepository_userId_idx" ON "MonitoredRepository"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "MonitoredRepository_userId_repoId_key" ON "MonitoredRepository"("userId", "repoId");

-- AddForeignKey
ALTER TABLE "Account" ADD CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Session" ADD CONSTRAINT "Session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MonitoredRepository" ADD CONSTRAINT "MonitoredRepository_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReviewStats" ADD CONSTRAINT "ReviewStats_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReviewSettings" ADD CONSTRAINT "ReviewSettings_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
