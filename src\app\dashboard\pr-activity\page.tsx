'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import {
  GitPullRequest,
  Search,
  ExternalLink,
  Clock,
  User,
  GitBranch,
  Filter,
  RefreshCw,
  Eye,
  CheckCircle,
  XCircle,
  AlertCircle,
  Calendar,
  Hash
} from 'lucide-react';

interface PR {
  id: number;
  number: number;
  title: string;
  state: string;
  html_url: string;
  repo: string;
  owner: string;
  user: string;
  created_at: string;
  updated_at: string;
}

export default function PRActivityPage() {
  const [prs, setPRs] = useState<PR[]>([]);
  const [filteredPRs, setFilteredPRs] = useState<PR[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'open' | 'closed'>('all');
  const [refreshing, setRefreshing] = useState(false);

  const fetchPRs = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await fetch('/api/github/pull-requests');
      if (!res.ok) throw new Error('Failed to fetch pull requests');
      const data = await res.json();
      setPRs(data);
      setFilteredPRs(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const refreshPRs = async () => {
    setRefreshing(true);
    await fetchPRs();
    setRefreshing(false);
  };

  useEffect(() => {
    fetchPRs();
  }, []);

  // Filter PRs based on search term and status
  useEffect(() => {
    let filtered = prs;

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(pr => pr.state === statusFilter);
    }

    // Filter by search term
    if (searchTerm.trim() !== '') {
      filtered = filtered.filter(pr =>
        pr.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pr.repo.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pr.owner.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pr.user.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredPRs(filtered);
  }, [prs, searchTerm, statusFilter]);

  const getStatusIcon = (state: string) => {
    switch (state) {
      case 'open':
        return <AlertCircle className="h-4 w-4 text-green-400" />;
      case 'closed':
        return <CheckCircle className="h-4 w-4 text-gray-400" />;
      case 'merged':
        return <CheckCircle className="h-4 w-4 text-purple-400" />;
      default:
        return <XCircle className="h-4 w-4 text-red-400" />;
    }
  };

  const getStatusColor = (state: string) => {
    switch (state) {
      case 'open':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'closed':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'merged':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-red-100 text-red-800 border-red-200';
    }
  };

  return (
    <main
      className="p-6"
      style={{backgroundColor: 'var(--background)', minHeight: '100vh'}}
    >
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <header className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-3">
            <GitPullRequest className="h-8 w-8" style={{color: 'var(--primary)'}} />
            <div>
              <h1 className="text-3xl font-bold" style={{color: 'var(--foreground)'}}>
                Pull Request Activity
              </h1>
              <p className="text-sm mt-1" style={{color: 'var(--muted-foreground)'}}>
                Monitor and review pull requests across your repositories
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={refreshPRs}
              disabled={refreshing}
              className="flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors hover:opacity-80"
              style={{
                backgroundColor: 'var(--secondary)',
                color: 'var(--secondary-foreground)'
              }}
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              <span>{refreshing ? 'Refreshing...' : 'Refresh'}</span>
            </button>
          </div>
        </header>

        {/* Filters and Search */}
        <section
          className="rounded-lg p-6 mb-6"
          style={{
            backgroundColor: 'var(--card)',
            border: '1px solid var(--border)'
          }}
        >
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4" style={{color: 'var(--muted-foreground)'}} />
                <span className="text-sm font-medium" style={{color: 'var(--foreground)'}}>Filter by status:</span>
              </div>
              <div className="flex space-x-2">
                {['all', 'open', 'closed'].map((status) => (
                  <button
                    key={status}
                    onClick={() => setStatusFilter(status as any)}
                    className={`px-3 py-1 text-xs rounded-lg transition-colors ${
                      statusFilter === status ? 'font-medium' : ''
                    }`}
                    style={{
                      backgroundColor: statusFilter === status ? 'var(--primary)' : 'var(--secondary)',
                      color: statusFilter === status ? 'var(--primary-foreground)' : 'var(--secondary-foreground)'
                    }}
                  >
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </button>
                ))}
              </div>
            </div>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4" style={{color: 'var(--muted-foreground)'}} />
              <input
                type="text"
                placeholder="Search pull requests..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-50 w-64"
                style={{
                  backgroundColor: 'var(--background)',
                  borderColor: 'var(--border)',
                  color: 'var(--foreground)'
                }}
              />
            </div>
          </div>
        </section>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div
            className="rounded-lg p-4"
            style={{
              backgroundColor: 'var(--card)',
              border: '1px solid var(--border)'
            }}
          >
            <div className="flex items-center space-x-2">
              <GitPullRequest className="h-5 w-5" style={{color: 'var(--primary)'}} />
              <span className="text-sm font-medium" style={{color: 'var(--muted-foreground)'}}>Total PRs</span>
            </div>
            <p className="text-2xl font-bold mt-1" style={{color: 'var(--foreground)'}}>{prs.length}</p>
          </div>
          <div
            className="rounded-lg p-4"
            style={{
              backgroundColor: 'var(--card)',
              border: '1px solid var(--border)'
            }}
          >
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-green-400" />
              <span className="text-sm font-medium" style={{color: 'var(--muted-foreground)'}}>Open PRs</span>
            </div>
            <p className="text-2xl font-bold mt-1" style={{color: 'var(--foreground)'}}>
              {prs.filter(pr => pr.state === 'open').length}
            </p>
          </div>
          <div
            className="rounded-lg p-4"
            style={{
              backgroundColor: 'var(--card)',
              border: '1px solid var(--border)'
            }}
          >
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-gray-400" />
              <span className="text-sm font-medium" style={{color: 'var(--muted-foreground)'}}>Closed PRs</span>
            </div>
            <p className="text-2xl font-bold mt-1" style={{color: 'var(--foreground)'}}>
              {prs.filter(pr => pr.state === 'closed').length}
            </p>
          </div>
        </div>

        {/* Main Content */}
        <section
          className="rounded-lg p-6"
          style={{
            backgroundColor: 'var(--card)',
            border: '1px solid var(--border)'
          }}
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
              Pull Requests ({filteredPRs.length})
            </h2>
          </div>

          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style={{borderColor: 'var(--primary)'}}></div>
              <p style={{color: 'var(--muted-foreground)'}}>Loading pull requests...</p>
            </div>
          ) : error ? (
            <div
              className="text-center py-12 rounded-lg"
              style={{
                backgroundColor: 'var(--destructive)',
                color: 'var(--destructive-foreground)'
              }}
            >
              <XCircle className="h-8 w-8 mx-auto mb-2" />
              <p>{error}</p>
            </div>
          ) : filteredPRs.length === 0 ? (
            <div className="text-center py-12" style={{color: 'var(--muted-foreground)'}}>
              <GitPullRequest className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p className="text-lg font-medium mb-1">No pull requests found</p>
              <p className="text-sm">
                {prs.length === 0
                  ? 'No pull requests found for your monitored repositories.'
                  : 'Try adjusting your search or filter criteria.'
                }
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredPRs.map(pr => (
                <div
                  key={pr.id}
                  className="rounded-lg p-4 border transition-all hover:shadow-md"
                  style={{
                    backgroundColor: 'var(--background)',
                    borderColor: 'var(--border)'
                  }}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        {getStatusIcon(pr.state)}
                        <a
                          href={pr.html_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="font-semibold hover:underline truncate"
                          style={{color: 'var(--primary)'}}
                        >
                          {pr.title}
                        </a>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(pr.state)}`}
                        >
                          {pr.state}
                        </span>
                      </div>

                      <div className="flex items-center space-x-4 text-sm" style={{color: 'var(--muted-foreground)'}}>
                        <div className="flex items-center space-x-1">
                          <Hash className="h-3 w-3" />
                          <span>{pr.number}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <GitBranch className="h-3 w-3" />
                          <span className="font-mono">{pr.owner}/{pr.repo}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <User className="h-3 w-3" />
                          <span>{pr.user}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>{new Date(pr.created_at).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      <a
                        href={pr.html_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 rounded-lg hover:opacity-80 transition-colors"
                        style={{
                          backgroundColor: 'var(--secondary)',
                          color: 'var(--secondary-foreground)'
                        }}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </a>
                      <Link
                        href={`/dashboard/prs/${pr.owner}/${pr.repo}/${pr.number}`}
                        className="flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors hover:opacity-90"
                        style={{
                          backgroundColor: 'var(--primary)',
                          color: 'var(--primary-foreground)'
                        }}
                      >
                        <Eye className="h-4 w-4" />
                        <span>Review</span>
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </section>
      </div>
    </main>
  );
}