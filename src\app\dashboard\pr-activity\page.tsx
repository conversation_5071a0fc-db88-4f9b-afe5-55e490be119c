'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';

interface PR {
  id: number;
  number: number;
  title: string;
  state: string;
  html_url: string;
  repo: string;
  owner: string;
  user: string;
  created_at: string;
  updated_at: string;
}

export default function PRActivityPage() {
  const [prs, setPRs] = useState<PR[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchPRs() {
      setLoading(true);
      setError(null);
      try {
        const res = await fetch('/api/github/pull-requests');
        if (!res.ok) throw new Error('Failed to fetch pull requests');
        const data = await res.json();
        setPRs(data);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    fetchPRs();
  }, []);

  return (
    <div className="p-8">
      <h2 className="text-3xl font-bold mb-4 text-white">Pull Request Activity</h2>
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-xl font-semibold mb-4">Recent Pull Requests</h3>
        {loading ? (
          <div>Loading pull requests...</div>
        ) : error ? (
          <div className="text-red-600">{error}</div>
        ) : prs.length === 0 ? (
          <div className="text-gray-500">No recent pull requests found for your monitored repositories.</div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {prs.map(pr => (
              <li key={pr.id} className="py-4 flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                <div>
                  <a href={pr.html_url} target="_blank" rel="noopener noreferrer" className="font-semibold text-blue-700 hover:underline">
                    {pr.title}
                  </a>
                  <div className="text-xs text-gray-500">
                    #{pr.number} in <span className="font-mono">{pr.owner}/{pr.repo}</span> by <span className="font-mono">{pr.user}</span>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <span className={`px-2 py-1 rounded text-xs font-semibold ${pr.state === 'open' ? 'bg-green-100 text-green-700' : 'bg-gray-200 text-gray-700'}`}>{pr.state}</span>
                  <span className="text-xs text-gray-400">Created: {new Date(pr.created_at).toLocaleString()}</span>
                  <Link href={`/dashboard/prs/${pr.owner}/${pr.repo}/${pr.number}`} className="bg-blue-600 text-white px-4 py-1 rounded hover:bg-blue-700 text-sm font-semibold">Review</Link>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
} 