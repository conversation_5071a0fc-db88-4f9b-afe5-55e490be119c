'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import {
  GitBranch,
  Search,
  ExternalLink,
  Check,
  X,
  ArrowLeft,
  Shield,
  Eye,
  Star,
  RefreshCw,
  Save
} from 'lucide-react';

interface Repo {
  id: number;
  name: string;
  full_name: string;
  owner: string;
  private: boolean;
  html_url: string;
  description: string;
}

interface MonitoredRepo {
  repoId: string;
  repoName: string;
  repoOwner: string;
  html_url?: string;
}

export default function RepositoriesPage() {
  const [repos, setRepos] = useState<Repo[]>([]);
  const [filteredRepos, setFilteredRepos] = useState<Repo[]>([]);
  const [selected, setSelected] = useState<Set<number>>(new Set());
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [monitored, setMonitored] = useState<MonitoredRepo[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      setError(null);
      try {
        // Fetch monitored repos first
        const monitoredRes = await fetch('/api/monitored-repos');
        let monitoredList: MonitoredRepo[] = [];
        if (monitoredRes.ok) {
          monitoredList = await monitoredRes.json();
          setMonitored(monitoredList);
        }
        // Fetch all repos
        const res = await fetch('/api/github/repos');
        if (!res.ok) throw new Error('Failed to fetch repositories');
        const data = await res.json();
        setRepos(data);
        setFilteredRepos(data);
        // Pre-select monitored repos
        setSelected(new Set(data.filter((r: Repo) => monitoredList.some(m => m.repoId === String(r.id))).map((r: Repo) => r.id)));
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  // Search functionality
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredRepos(repos);
    } else {
      const filtered = repos.filter(repo =>
        repo.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        repo.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredRepos(filtered);
    }
  }, [searchTerm, repos]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        if (e.key === 's') {
          e.preventDefault();
          if (selected.size > 0 && !saving) {
            saveSelection();
          }
        }
        if (e.key === 'a') {
          e.preventDefault();
          // Select all visible repositories
          const allVisible = new Set(filteredRepos.map(repo => repo.id));
          setSelected(allVisible);
        }
      }
      if (e.key === 'Escape') {
        setSearchTerm('');
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [filteredRepos, selected.size, saving]);

  const toggleRepo = (id: number) => {
    setSelected(prev => {
      const next = new Set(prev);
      if (next.has(id)) next.delete(id);
      else next.add(id);
      return next;
    });
  };

  const selectAll = () => {
    const allVisible = new Set(filteredRepos.map(repo => repo.id));
    setSelected(allVisible);
  };

  const clearSelection = () => {
    setSelected(new Set());
  };

  const saveSelection = async () => {
    setSaving(true);
    setError(null);
    setSuccess(false);
    try {
      const selectedRepos = repos.filter(r => selected.has(r.id));
      const res = await fetch('/api/monitored-repos', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ repos: selectedRepos }),
      });
      if (!res.ok) throw new Error('Failed to save selection');
      setSuccess(true);
      // Update monitored set after save
      setMonitored(selectedRepos.map(r => ({ repoId: String(r.id), repoName: r.name, repoOwner: r.owner, html_url: r.html_url })));
    } catch (err: any) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  };

  return (
    <main
      className="min-h-screen p-6"
      style={{backgroundColor: 'var(--background)'}}
    >
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <header className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link
              href="/dashboard"
              className="flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors hover:opacity-80"
              style={{
                backgroundColor: 'var(--secondary)',
                color: 'var(--secondary-foreground)'
              }}
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Dashboard</span>
            </Link>
            <div>
              <h1 className="text-3xl font-bold" style={{color: 'var(--foreground)'}}>
                Repository Management
              </h1>
              <p className="text-sm mt-1" style={{color: 'var(--muted-foreground)'}}>
                Select repositories to monitor for AI-powered code reviews
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm" style={{color: 'var(--muted-foreground)'}}>
              {selected.size} selected
            </span>
          </div>
        </header>

        {/* Currently Monitored Section */}
        <section
          className="rounded-lg p-6 mb-8"
          style={{
            backgroundColor: 'var(--card)',
            border: '1px solid var(--border)'
          }}
        >
          <div className="flex items-center space-x-2 mb-4">
            <GitBranch className="h-5 w-5" style={{color: 'var(--primary)'}} />
            <h2 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
              Currently Monitored ({monitored.length})
            </h2>
          </div>

          {monitored.length === 0 ? (
            <div
              className="text-center py-8 rounded-lg border-2 border-dashed"
              style={{
                borderColor: 'var(--border)',
                color: 'var(--muted-foreground)'
              }}
            >
              <GitBranch className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p className="text-lg font-medium mb-1">No repositories monitored yet</p>
              <p className="text-sm">Select repositories below to start monitoring</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {monitored.map(repo => (
                <div
                  key={repo.repoId}
                  className="rounded-lg p-4 transition-all hover:shadow-lg"
                  style={{
                    backgroundColor: 'var(--accent)',
                    border: '1px solid var(--border)'
                  }}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <GitBranch className="h-4 w-4" style={{color: 'var(--primary)'}} />
                      <span className="font-medium text-sm" style={{color: 'var(--foreground)'}}>
                        {repo.repoOwner}/{repo.repoName}
                      </span>
                    </div>
                    <a
                      href={repo.html_url || `https://github.com/${repo.repoOwner}/${repo.repoName}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-1 rounded hover:opacity-80"
                      style={{color: 'var(--muted-foreground)'}}
                    >
                      <ExternalLink className="h-3 w-3" />
                    </a>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-2 h-2 rounded-full"
                      style={{backgroundColor: 'var(--primary)'}}
                    ></div>
                    <span className="text-xs" style={{color: 'var(--muted-foreground)'}}>
                      Active monitoring
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </section>

        {/* Repository Selection Section */}
        <section
          className="rounded-lg p-6"
          style={{
            backgroundColor: 'var(--card)',
            border: '1px solid var(--border)'
          }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <Search className="h-5 w-5" style={{color: 'var(--primary)'}} />
              <h2 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                Available Repositories
              </h2>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <button
                  type="button"
                  onClick={selectAll}
                  className="px-3 py-1 text-xs rounded-lg transition-colors hover:opacity-80"
                  style={{
                    backgroundColor: 'var(--secondary)',
                    color: 'var(--secondary-foreground)'
                  }}
                >
                  Select All
                </button>
                <button
                  type="button"
                  onClick={clearSelection}
                  className="px-3 py-1 text-xs rounded-lg transition-colors hover:opacity-80"
                  style={{
                    backgroundColor: 'var(--secondary)',
                    color: 'var(--secondary-foreground)'
                  }}
                >
                  Clear
                </button>
              </div>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4" style={{color: 'var(--muted-foreground)'}} />
                <input
                  type="text"
                  placeholder="Search repositories... (Esc to clear)"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-50 w-64"
                  style={{
                    backgroundColor: 'var(--background)',
                    borderColor: 'var(--border)',
                    color: 'var(--foreground)',
                    focusRingColor: 'var(--primary)'
                  }}
                />
              </div>
            </div>
          </div>

          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style={{borderColor: 'var(--primary)'}}></div>
              <p style={{color: 'var(--muted-foreground)'}}>Loading repositories...</p>
            </div>
          ) : error ? (
            <div
              className="text-center py-12 rounded-lg"
              style={{
                backgroundColor: 'var(--destructive)',
                color: 'var(--destructive-foreground)'
              }}
            >
              <X className="h-8 w-8 mx-auto mb-2" />
              <p>{error}</p>
            </div>
          ) : (
            <form
              onSubmit={e => {
                e.preventDefault();
                saveSelection();
              }}
            >
              <div className="space-y-3 mb-6 max-h-96 overflow-y-auto">
                {filteredRepos.length === 0 ? (
                  <div className="text-center py-8" style={{color: 'var(--muted-foreground)'}}>
                    <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No repositories found matching your search.</p>
                  </div>
                ) : (
                  filteredRepos.map(repo => (
                    <div
                      key={repo.id}
                      className={`rounded-lg p-4 border transition-all hover:shadow-md cursor-pointer ${
                        selected.has(repo.id) ? 'ring-2 ring-opacity-50' : ''
                      }`}
                      style={{
                        backgroundColor: selected.has(repo.id) ? 'var(--accent)' : 'var(--background)',
                        borderColor: selected.has(repo.id) ? 'var(--primary)' : 'var(--border)',
                        ringColor: selected.has(repo.id) ? 'var(--primary)' : 'transparent'
                      }}
                      onClick={() => toggleRepo(repo.id)}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="flex items-center pt-1">
                          <input
                            type="checkbox"
                            checked={selected.has(repo.id)}
                            onChange={() => toggleRepo(repo.id)}
                            id={`repo-${repo.id}`}
                            className="w-4 h-4 rounded border-2 focus:ring-2 focus:ring-opacity-50"
                            style={{
                              accentColor: 'var(--primary)',
                              borderColor: 'var(--border)'
                            }}
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <GitBranch className="h-4 w-4 flex-shrink-0" style={{color: 'var(--primary)'}} />
                            <span className="font-semibold truncate" style={{color: 'var(--foreground)'}}>
                              {repo.full_name}
                            </span>
                            {repo.private && (
                              <div className="flex items-center space-x-1">
                                <Shield className="h-3 w-3" style={{color: 'var(--muted-foreground)'}} />
                                <span className="text-xs" style={{color: 'var(--muted-foreground)'}}>Private</span>
                              </div>
                            )}
                          </div>
                          {repo.description && (
                            <p className="text-sm line-clamp-2" style={{color: 'var(--muted-foreground)'}}>
                              {repo.description}
                            </p>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          <a
                            href={repo.html_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-2 rounded-lg hover:opacity-80 transition-colors"
                            style={{
                              backgroundColor: 'var(--secondary)',
                              color: 'var(--secondary-foreground)'
                            }}
                            onClick={(e) => e.stopPropagation()}
                          >
                            <ExternalLink className="h-4 w-4" />
                          </a>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>

              <div className="flex items-center justify-between pt-4 border-t" style={{borderColor: 'var(--border)'}}>
                <div className="flex items-center space-x-4">
                  <div className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                    {selected.size} of {filteredRepos.length} repositories selected
                  </div>
                  <div className="text-xs" style={{color: 'var(--muted-foreground)'}}>
                    <span className="hidden md:inline">
                      Shortcuts: Ctrl+A (select all), Ctrl+S (save), Esc (clear search)
                    </span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  {success && (
                    <div className="flex items-center space-x-2 text-green-400">
                      <Check className="h-4 w-4" />
                      <span className="text-sm">Selection saved!</span>
                    </div>
                  )}
                  <button
                    type="submit"
                    disabled={saving || selected.size === 0}
                    className="flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-all hover:opacity-90 disabled:opacity-50"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--primary-foreground)'
                    }}
                  >
                    {saving ? (
                      <>
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span>Saving...</span>
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4" />
                        <span>Save Selection ({selected.size})</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </form>
          )}
        </section>
      </div>
    </main>
  );
}