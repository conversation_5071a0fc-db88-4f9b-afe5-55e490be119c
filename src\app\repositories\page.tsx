'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';

interface Repo {
  id: number;
  name: string;
  full_name: string;
  owner: string;
  private: boolean;
  html_url: string;
  description: string;
}

interface MonitoredRepo {
  repoId: string;
  repoName: string;
  repoOwner: string;
  html_url?: string;
}

export default function RepositoriesPage() {
  const [repos, setRepos] = useState<Repo[]>([]);
  const [selected, setSelected] = useState<Set<number>>(new Set());
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [monitored, setMonitored] = useState<MonitoredRepo[]>([]);

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      setError(null);
      try {
        // Fetch monitored repos first
        const monitoredRes = await fetch('/api/monitored-repos');
        let monitoredList: MonitoredRepo[] = [];
        if (monitoredRes.ok) {
          monitoredList = await monitoredRes.json();
          setMonitored(monitoredList);
        }
        // Fetch all repos
        const res = await fetch('/api/github/repos');
        if (!res.ok) throw new Error('Failed to fetch repositories');
        const data = await res.json();
        setRepos(data);
        // Pre-select monitored repos
        setSelected(new Set(data.filter((r: Repo) => monitoredList.some(m => m.repoId === String(r.id))).map((r: Repo) => r.id)));
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  const toggleRepo = (id: number) => {
    setSelected(prev => {
      const next = new Set(prev);
      if (next.has(id)) next.delete(id);
      else next.add(id);
      return next;
    });
  };

  const saveSelection = async () => {
    setSaving(true);
    setError(null);
    setSuccess(false);
    try {
      const selectedRepos = repos.filter(r => selected.has(r.id));
      const res = await fetch('/api/monitored-repos', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ repos: selectedRepos }),
      });
      if (!res.ok) throw new Error('Failed to save selection');
      setSuccess(true);
      // Update monitored set after save
      setMonitored(selectedRepos.map(r => ({ repoId: String(r.id), repoName: r.name, repoOwner: r.owner, html_url: r.html_url })));
    } catch (err: any) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  };

  return (
    <main className="max-w-4xl mx-auto p-8">
      {/* Header */}
      <header className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">Select Repositories to Monitor</h1>
        <Link href="/dashboard" className="text-blue-700 hover:underline font-medium">Back to Dashboard</Link>
      </header>

      {/* Monitored summary */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-2">Currently Monitored</h2>
        {monitored.length === 0 ? (
          <div className="text-gray-500">No repositories are currently being monitored.</div>
        ) : (
          <ul className="flex flex-wrap gap-2">
            {monitored.map(repo => (
              <li key={repo.repoId} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center gap-2">
                <a href={repo.html_url || `https://github.com/${repo.repoOwner}/${repo.repoName}`} target="_blank" rel="noopener noreferrer" className="hover:underline">
                  {repo.repoOwner}/{repo.repoName}
                </a>
              </li>
            ))}
          </ul>
        )}
      </section>

      {loading ? (
        <div>Loading repositories...</div>
      ) : error ? (
        <div className="text-red-600">{error}</div>
      ) : (
        <form
          onSubmit={e => {
            e.preventDefault();
            saveSelection();
          }}
        >
          <ul className="space-y-2 mb-6">
            {repos.map(repo => (
              <li key={repo.id} className={`flex items-center gap-2 bg-white rounded p-3 shadow border ${selected.has(repo.id) ? 'border-blue-500' : 'border-gray-200'}`}>
                <input
                  type="checkbox"
                  checked={selected.has(repo.id)}
                  onChange={() => toggleRepo(repo.id)}
                  id={`repo-${repo.id}`}
                  className="accent-blue-600 scale-125"
                />
                <label htmlFor={`repo-${repo.id}`} className="flex-1 cursor-pointer">
                  <span className="font-semibold text-lg">{repo.full_name}</span>
                  {repo.private && <span className="ml-2 text-xs text-gray-500">(Private)</span>}
                  <div className="text-xs text-gray-600">{repo.description}</div>
                </label>
                <a href={repo.html_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 text-xs underline">View</a>
              </li>
            ))}
          </ul>
          <button
            type="submit"
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50 font-semibold text-lg"
            disabled={saving || selected.size === 0}
          >
            {saving ? 'Saving...' : 'Monitor Selected'}
          </button>
          {success && <div className="text-green-600 mt-4">Selection saved!</div>}
        </form>
      )}
    </main>
  );
} 