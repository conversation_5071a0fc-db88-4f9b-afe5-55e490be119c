import { NextRequest, NextResponse } from 'next/server';
import { openrouterChatCompletion, DEEPSEEK_MODEL } from '@/lib/deepseek';

const SYSTEM_PROMPT = `You are an expert code reviewer AI assistant. Given a code review and a user prompt, reformat or rewrite the review according to the prompt. Only return the new review text.`;

export async function POST(req: NextRequest) {
  try {
    const { review, prompt } = await req.json();
    if (!review || !prompt) {
      return NextResponse.json({ error: 'Missing review or prompt' }, { status: 400 });
    }
    const userPrompt = `User prompt: ${prompt}

Current review:
${review}
`;
    const aiResponse = await openrouterChatCompletion(
      [
        { role: 'system', content: SYSTEM_PROMPT },
        { role: 'user', content: userPrompt },
      ],
      DEEPSEEK_MODEL
    );
    return NextResponse.json({ reformatted: aiResponse });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 