import { NextRequest, NextResponse } from 'next/server';
import { openrouterChatCompletion, DEEPSEEK_MODEL, LLAMA4_MAVERICK_MODEL } from '@/lib/deepseek';

const SYSTEM_PROMPT = `You are an expert code reviewer AI. Given a pull request diff, perform a thorough review covering the following:
1. Analyze code diffs and generate actionable review comments.
2. Detect potential bugs, code smells, and anti-patterns.
3. Suggest improvements and refactoring opportunities.
4. Highlight security vulnerabilities and risky code.
5. Check for adherence to coding standards and best practices.
6. Provide inline comments (if possible) and a summary comment.

For inline comments, ONLY use the following format for lines in the diff that require a comment:
[File: <relative file path> | Line: <line number in the new file>]
Comment: <your comment>

- Only generate inline comments for lines that truly need feedback or suggestions.
- Use the correct file path and line number as shown in the diff.
- Do not generate inline comments for unchanged or context lines.
- After inline comments, provide a clear summary section.
- If no inline comments are needed, just provide the summary.

Return your review in markdown with clear sections for each point.
`;

export async function POST(req: NextRequest) {
  try {
    const { diff, prTitle, prDescription, files } = await req.json();
    if (!diff) {
      console.error('Missing diff in request');
      return NextResponse.json({ error: 'Missing diff' }, { status: 400 });
    }

    // Model selection logic
    const DEEPSEEK_MAX_CHARS = 100000; // ~128k tokens safe
    let model = DEEPSEEK_MODEL;
    let modelName = 'DeepSeek Coder V2';
    if (diff.length > DEEPSEEK_MAX_CHARS) {
      model = LLAMA4_MAVERICK_MODEL;
      modelName = 'Llama 4 Maverick';
    }

    // Optionally truncate for Llama 4 Maverick if you want a hard limit (e.g., 900,000 chars)
    const LLAMA4_MAX_CHARS = 900000;
    let safeDiff = diff;
    let truncated = false;
    if (model === LLAMA4_MAVERICK_MODEL && diff.length > LLAMA4_MAX_CHARS) {
      safeDiff = diff.slice(0, LLAMA4_MAX_CHARS);
      truncated = true;
    }

    console.log(`Calling ${modelName} with PR:`, prTitle, 'Diff length:', safeDiff.length, 'Truncated:', truncated);

    const userPrompt = `Pull Request Title: ${prTitle || ''}
Description: ${prDescription || ''}

Diff:
${safeDiff}
`;

    let aiResponse = await openrouterChatCompletion(
      [
        { role: 'system', content: SYSTEM_PROMPT },
        { role: 'user', content: userPrompt }
      ],
      model
    );

    if (truncated) {
      aiResponse = `**[WARNING: The diff was too large and has been truncated for this review. Only the first ${LLAMA4_MAX_CHARS} characters are included. Results may be incomplete.]**\n\n` + aiResponse;
    }

    return NextResponse.json({ review: aiResponse, model: modelName });
  } catch (error: any) {
    console.error('AI review error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 