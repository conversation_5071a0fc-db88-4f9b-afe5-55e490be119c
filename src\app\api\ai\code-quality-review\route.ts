import { NextRequest, NextResponse } from 'next/server';
import { openrouterChatCompletion, DEEPSEEK_MODEL, LLAMA4_MAVERICK_MODEL } from '@/lib/deepseek';
import { prisma } from '@/lib/prisma';

const SYSTEM_PROMPT = `You are an expert code reviewer AI. Given a pull request diff, title, and description, analyze the code for code quality, security, performance, and best practices. Return a JSON array of review items. Each item should have:
- type: one of ["security", "performance", "quality", "tip"]
- level: one of ["critical", "warning", "info", "success"]
- title: short summary (e.g., "Critical Security Issue")
- message: detailed explanation
- code: (optional) code snippet or example
- line: (optional) line number or file reference

Example output:
[
  { "type": "security", "level": "critical", "title": "Critical Security Issue", "message": "SQL injection vulnerability detected.", "code": "query = \"SELECT * FROM users WHERE id = ?\"", "line": 42 },
  { "type": "performance", "level": "warning", "title": "Performance Optimization", "message": "Consider memoizing this calculation.", "code": "const memoized = useMemo(...)" },
  { "type": "quality", "level": "success", "title": "Excellent Code Quality", "message": "Great use of TypeScript interfaces." }
]
Only return the JSON array.`;

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const repositoryId = searchParams.get('repositoryId');
  const pullRequestId = searchParams.get('pullRequestId');
  if (!repositoryId || !pullRequestId) {
    return NextResponse.json({ error: 'Missing parameters' }, { status: 400 });
  }
  const review = await prisma.codeQualityReview.findUnique({
    where: { pullRequestId_repositoryId: { pullRequestId, repositoryId } },
  });
  if (!review) return NextResponse.json({});
  return NextResponse.json({ review: review.review });
}

export async function POST(req: NextRequest) {
  try {
    const { diff, prTitle, prDescription, repositoryId, pullRequestId } = await req.json();
    if (!diff || !repositoryId || !pullRequestId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Hybrid model selection logic
    const DEEPSEEK_MAX_CHARS = 100000; // ~128k tokens safe
    const LLAMA4_MAX_CHARS = 900000;   // Llama 4 Maverick max context
    let model = DEEPSEEK_MODEL;
    let modelName = 'DeepSeek Coder V2';
    let safeDiff = diff;
    let truncated = false;

    if (diff.length > DEEPSEEK_MAX_CHARS) {
      model = LLAMA4_MAVERICK_MODEL;
      modelName = 'Llama 4 Maverick';
      if (diff.length > LLAMA4_MAX_CHARS) {
        safeDiff = diff.slice(0, LLAMA4_MAX_CHARS);
        truncated = true;
      }
    }

    const userPrompt = `Pull Request Title: ${prTitle || ''}\nDescription: ${prDescription || ''}\n\nDiff:\n${safeDiff}`;
    const aiResponse = await openrouterChatCompletion(
      [
        { role: 'system', content: SYSTEM_PROMPT },
        { role: 'user', content: userPrompt },
      ],
      model
    );
    let reviewItems;
    try {
      reviewItems = JSON.parse(aiResponse);
    } catch (e) {
      return NextResponse.json({ error: 'AI response could not be parsed as JSON', raw: aiResponse }, { status: 500 });
    }
    // Store in DB
    const saved = await prisma.codeQualityReview.upsert({
      where: { pullRequestId_repositoryId: { pullRequestId, repositoryId } },
      update: { review: reviewItems },
      create: { pullRequestId, repositoryId, review: reviewItems },
    });
    return NextResponse.json({ review: saved.review, truncated, model: modelName });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 