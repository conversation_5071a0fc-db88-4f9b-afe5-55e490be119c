'use client';

import { useEffect, useState } from 'react';
import { Line, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface ReviewStats {
  totalComments: number;
  inlineComments: number;
  summaryComments: number;
  bugsFound: number;
  suggestionsMade: number;
  securityIssues: number;
  reviewTimeMs: number;
  createdAt: string;
}

export default function AnalyticsPage() {
  const [stats, setStats] = useState<ReviewStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year'>('week');

  useEffect(() => {
    async function fetchStats() {
      try {
        const res = await fetch(`/api/analytics/stats?range=${timeRange}`);
        if (!res.ok) throw new Error('Failed to fetch analytics');
        const data = await res.json();
        setStats(data);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    fetchStats();
  }, [timeRange]);

  const chartData = {
    labels: stats.map(s => new Date(s.createdAt).toLocaleDateString()),
    datasets: [
      {
        label: 'Total Comments',
        data: stats.map(s => s.totalComments),
        borderColor: 'rgb(75, 192, 192)',
        tension: 0.1,
      },
      {
        label: 'Bugs Found',
        data: stats.map(s => s.bugsFound),
        borderColor: 'rgb(255, 99, 132)',
        tension: 0.1,
      },
      {
        label: 'Security Issues',
        data: stats.map(s => s.securityIssues),
        borderColor: 'rgb(255, 159, 64)',
        tension: 0.1,
      },
    ],
  };

  const issuesData = {
    labels: ['Bugs', 'Security Issues', 'Suggestions'],
    datasets: [
      {
        label: 'Issues Found',
        data: [
          stats.reduce((sum, s) => sum + s.bugsFound, 0),
          stats.reduce((sum, s) => sum + s.securityIssues, 0),
          stats.reduce((sum, s) => sum + s.suggestionsMade, 0),
        ],
        backgroundColor: [
          'rgba(255, 99, 132, 0.5)',
          'rgba(255, 159, 64, 0.5)',
          'rgba(75, 192, 192, 0.5)',
        ],
      },
    ],
  };

  if (loading) return <div className="p-8">Loading analytics...</div>;
  if (error) return <div className="p-8 text-red-600">{error}</div>;

  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-8">
        <h2 className="text-3xl font-bold text-white">Analytics & Insights</h2>
        <div className="flex gap-2">
          <button
            onClick={() => setTimeRange('week')}
            className={`px-4 py-2 rounded ${timeRange === 'week' ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300'}`}
          >
            Week
          </button>
          <button
            onClick={() => setTimeRange('month')}
            className={`px-4 py-2 rounded ${timeRange === 'month' ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300'}`}
          >
            Month
          </button>
          <button
            onClick={() => setTimeRange('year')}
            className={`px-4 py-2 rounded ${timeRange === 'year' ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300'}`}
          >
            Year
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {/* Summary Cards */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-xl font-semibold mb-4">Review Activity</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-gray-600">Total Reviews</p>
              <p className="text-2xl font-bold">{stats.length}</p>
            </div>
            <div>
              <p className="text-gray-600">Avg. Review Time</p>
              <p className="text-2xl font-bold">
                {Math.round(stats.reduce((sum, s) => sum + s.reviewTimeMs, 0) / stats.length / 1000)}s
              </p>
            </div>
            <div>
              <p className="text-gray-600">Total Comments</p>
              <p className="text-2xl font-bold">
                {stats.reduce((sum, s) => sum + s.totalComments, 0)}
              </p>
            </div>
            <div>
              <p className="text-gray-600">Issues Found</p>
              <p className="text-2xl font-bold">
                {stats.reduce((sum, s) => sum + s.bugsFound + s.securityIssues, 0)}
              </p>
            </div>
          </div>
        </div>

        {/* Issues Breakdown */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-xl font-semibold mb-4">Issues Breakdown</h3>
          <div className="h-64">
            <Bar data={issuesData} options={{ maintainAspectRatio: false }} />
          </div>
        </div>
      </div>

      {/* Review Activity Chart */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-xl font-semibold mb-4">Review Activity Over Time</h3>
        <div className="h-96">
          <Line data={chartData} options={{ maintainAspectRatio: false }} />
        </div>
      </div>
    </div>
  );
} 