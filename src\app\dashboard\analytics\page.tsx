'use client';

import { useEffect, useState } from 'react';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import {
  BarChart3,
  TrendingUp,
  Clock,
  MessageSquare,
  Bug,
  Shield,
  Lightbulb,
  RefreshCw,
  Download,
  Calendar,
  Target,
  Activity,
  AlertTriangle,
  CheckCircle,
  Zap
} from 'lucide-react';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

interface ReviewStats {
  totalComments: number;
  inlineComments: number;
  summaryComments: number;
  bugsFound: number;
  suggestionsMade: number;
  securityIssues: number;
  reviewTimeMs: number;
  createdAt: string;
}

export default function AnalyticsPage() {
  const [stats, setStats] = useState<ReviewStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year'>('week');
  const [refreshing, setRefreshing] = useState(false);

  const fetchStats = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await fetch(`/api/analytics/stats?range=${timeRange}`);
      if (!res.ok) throw new Error('Failed to fetch analytics');
      const data = await res.json();
      setStats(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const refreshStats = async () => {
    setRefreshing(true);
    await fetchStats();
    setRefreshing(false);
  };

  useEffect(() => {
    fetchStats();
  }, [timeRange]);

  // Calculate summary statistics
  const totalReviews = stats.length;
  const totalComments = stats.reduce((sum, s) => sum + s.totalComments, 0);
  const totalBugs = stats.reduce((sum, s) => sum + s.bugsFound, 0);
  const totalSecurity = stats.reduce((sum, s) => sum + s.securityIssues, 0);
  const totalSuggestions = stats.reduce((sum, s) => sum + s.suggestionsMade, 0);
  const avgReviewTime = totalReviews > 0 ? Math.round(stats.reduce((sum, s) => sum + s.reviewTimeMs, 0) / totalReviews / 1000) : 0;

  const chartData = {
    labels: stats.map(s => new Date(s.createdAt).toLocaleDateString()),
    datasets: [
      {
        label: 'Total Comments',
        data: stats.map(s => s.totalComments),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Bugs Found',
        data: stats.map(s => s.bugsFound),
        borderColor: 'rgb(239, 68, 68)',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Security Issues',
        data: stats.map(s => s.securityIssues),
        borderColor: 'rgb(245, 158, 11)',
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        tension: 0.4,
        fill: true,
      },
    ],
  };

  const issuesData = {
    labels: ['Bugs', 'Security Issues', 'Suggestions'],
    datasets: [
      {
        label: 'Issues Found',
        data: [totalBugs, totalSecurity, totalSuggestions],
        backgroundColor: [
          'rgba(239, 68, 68, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(16, 185, 129, 0.8)',
        ],
        borderColor: [
          'rgb(239, 68, 68)',
          'rgb(245, 158, 11)',
          'rgb(16, 185, 129)',
        ],
        borderWidth: 2,
      },
    ],
  };

  const performanceData = {
    labels: stats.map(s => new Date(s.createdAt).toLocaleDateString()),
    datasets: [
      {
        label: 'Review Time (seconds)',
        data: stats.map(s => s.reviewTimeMs / 1000),
        backgroundColor: 'rgba(168, 85, 247, 0.8)',
        borderColor: 'rgb(168, 85, 247)',
        borderWidth: 2,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: 'rgb(156, 163, 175)',
          font: { size: 12 }
        }
      },
    },
    scales: {
      x: {
        grid: { color: 'rgba(75, 85, 99, 0.3)' },
        ticks: { color: 'rgb(156, 163, 175)' }
      },
      y: {
        beginAtZero: true,
        grid: { color: 'rgba(75, 85, 99, 0.3)' },
        ticks: { color: 'rgb(156, 163, 175)' }
      },
    },
  };

  if (loading) {
    return (
      <main
        className="p-6"
        style={{backgroundColor: 'var(--background)', minHeight: '100vh'}}
      >
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style={{borderColor: 'var(--primary)'}}></div>
            <p style={{color: 'var(--muted-foreground)'}}>Loading analytics...</p>
          </div>
        </div>
      </main>
    );
  }

  if (error) {
    return (
      <main
        className="p-6"
        style={{backgroundColor: 'var(--background)', minHeight: '100vh'}}
      >
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-red-400" />
            <p className="text-lg font-medium mb-2" style={{color: 'var(--foreground)'}}>Failed to Load Analytics</p>
            <p style={{color: 'var(--muted-foreground)'}}>{error}</p>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main
      className="p-6"
      style={{backgroundColor: 'var(--background)', minHeight: '100vh'}}
    >
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <header className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-3">
            <BarChart3 className="h-8 w-8" style={{color: 'var(--primary)'}} />
            <div>
              <h1 className="text-3xl font-bold" style={{color: 'var(--foreground)'}}>
                Analytics & Insights
              </h1>
              <p className="text-sm mt-1" style={{color: 'var(--muted-foreground)'}}>
                Track AI review performance and code quality metrics
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={refreshStats}
              disabled={refreshing}
              className="flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors hover:opacity-80"
              style={{
                backgroundColor: 'var(--secondary)',
                color: 'var(--secondary-foreground)'
              }}
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              <span>{refreshing ? 'Refreshing...' : 'Refresh'}</span>
            </button>
            <div className="flex space-x-1">
              {['week', 'month', 'year'].map((range) => (
                <button
                  key={range}
                  onClick={() => setTimeRange(range as any)}
                  className={`px-4 py-2 rounded-lg transition-colors ${
                    timeRange === range ? 'font-medium' : ''
                  }`}
                  style={{
                    backgroundColor: timeRange === range ? 'var(--primary)' : 'var(--secondary)',
                    color: timeRange === range ? 'var(--primary-foreground)' : 'var(--secondary-foreground)'
                  }}
                >
                  {range.charAt(0).toUpperCase() + range.slice(1)}
                </button>
              ))}
            </div>
          </div>
        </header>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {[
            {
              title: 'Total Reviews',
              value: totalReviews,
              icon: Activity,
              color: 'text-blue-400',
              bgColor: 'bg-blue-500/10'
            },
            {
              title: 'Avg. Review Time',
              value: `${avgReviewTime}s`,
              icon: Clock,
              color: 'text-purple-400',
              bgColor: 'bg-purple-500/10'
            },
            {
              title: 'Total Comments',
              value: totalComments,
              icon: MessageSquare,
              color: 'text-green-400',
              bgColor: 'bg-green-500/10'
            },
            {
              title: 'Issues Found',
              value: totalBugs + totalSecurity,
              icon: AlertTriangle,
              color: 'text-red-400',
              bgColor: 'bg-red-500/10'
            }
          ].map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div
                key={index}
                className="rounded-lg p-6"
                style={{
                  backgroundColor: 'var(--card)',
                  border: '1px solid var(--border)'
                }}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium" style={{color: 'var(--muted-foreground)'}}>
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold mt-1" style={{color: 'var(--foreground)'}}>
                      {stat.value}
                    </p>
                  </div>
                  <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Issues Breakdown */}
          <section
            className="rounded-lg p-6"
            style={{
              backgroundColor: 'var(--card)',
              border: '1px solid var(--border)'
            }}
          >
            <div className="flex items-center space-x-2 mb-6">
              <Target className="h-5 w-5" style={{color: 'var(--primary)'}} />
              <h2 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                Issues Breakdown
              </h2>
            </div>
            <div className="h-64">
              <Bar data={issuesData} options={chartOptions} />
            </div>
          </section>

          {/* Performance Metrics */}
          <section
            className="rounded-lg p-6"
            style={{
              backgroundColor: 'var(--card)',
              border: '1px solid var(--border)'
            }}
          >
            <div className="flex items-center space-x-2 mb-6">
              <Zap className="h-5 w-5" style={{color: 'var(--primary)'}} />
              <h2 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                Review Performance
              </h2>
            </div>
            <div className="h-64">
              <Bar data={performanceData} options={chartOptions} />
            </div>
          </section>
        </div>

        {/* Review Activity Chart */}
        <section
          className="rounded-lg p-6"
          style={{
            backgroundColor: 'var(--card)',
            border: '1px solid var(--border)'
          }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" style={{color: 'var(--primary)'}} />
              <h2 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                Review Activity Over Time
              </h2>
            </div>
            <button
              className="flex items-center space-x-2 px-3 py-1 rounded-lg text-sm transition-colors hover:opacity-80"
              style={{
                backgroundColor: 'var(--secondary)',
                color: 'var(--secondary-foreground)'
              }}
            >
              <Download className="h-4 w-4" />
              <span>Export</span>
            </button>
          </div>
          <div className="h-96">
            <Line data={chartData} options={chartOptions} />
          </div>
        </section>

        {/* Insights Section */}
        <section
          className="rounded-lg p-6 mt-8"
          style={{
            backgroundColor: 'var(--card)',
            border: '1px solid var(--border)'
          }}
        >
          <div className="flex items-center space-x-2 mb-6">
            <Lightbulb className="h-5 w-5" style={{color: 'var(--primary)'}} />
            <h2 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
              Key Insights
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div
              className="p-4 rounded-lg"
              style={{
                backgroundColor: 'var(--accent)',
                border: '1px solid var(--border)'
              }}
            >
              <div className="flex items-center space-x-2 mb-2">
                <Bug className="h-4 w-4 text-red-400" />
                <span className="font-medium" style={{color: 'var(--foreground)'}}>Bug Detection</span>
              </div>
              <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                {totalBugs > 0 ? `Found ${totalBugs} potential bugs across ${totalReviews} reviews` : 'No bugs detected in recent reviews'}
              </p>
            </div>
            <div
              className="p-4 rounded-lg"
              style={{
                backgroundColor: 'var(--accent)',
                border: '1px solid var(--border)'
              }}
            >
              <div className="flex items-center space-x-2 mb-2">
                <Shield className="h-4 w-4 text-yellow-400" />
                <span className="font-medium" style={{color: 'var(--foreground)'}}>Security</span>
              </div>
              <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                {totalSecurity > 0 ? `Identified ${totalSecurity} security concerns` : 'No security issues found'}
              </p>
            </div>
            <div
              className="p-4 rounded-lg"
              style={{
                backgroundColor: 'var(--accent)',
                border: '1px solid var(--border)'
              }}
            >
              <div className="flex items-center space-x-2 mb-2">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <span className="font-medium" style={{color: 'var(--foreground)'}}>Efficiency</span>
              </div>
              <p className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                Average review time of {avgReviewTime} seconds per review
              </p>
            </div>
          </div>
        </section>
      </div>
    </main>
  );
}