import { PrismaClient } from '@prisma/client';
import { analyzeCodeWithAI } from './deepseek';

const prisma = new PrismaClient();

interface CodeQualityMetrics {
  codeComplexity: number;
  testCoverage: number;
  codeStyle: number;
  securityScore: number;
  documentationScore: number;
}

interface CodeAnalysisResult {
  metrics: CodeQualityMetrics;
  suggestions: string[];
  issues: string[];
}

export async function calculateCodeQualityScore(
  pullRequestId: string,
  repositoryId: string,
  codeDiff: string
): Promise<CodeAnalysisResult> {
  // Analyze code using AI
  const aiAnalysis = await analyzeCodeWithAI(codeDiff);

  // Calculate individual metric scores
  const metrics: CodeQualityMetrics = {
    codeComplexity: calculateComplexityScore(aiAnalysis),
    testCoverage: calculateTestCoverageScore(aiAnalysis),
    codeStyle: calculateCodeStyleScore(aiAnalysis),
    securityScore: calculateSecurityScore(aiAnalysis),
    documentationScore: calculateDocumentationScore(aiAnalysis),
  };

  // Calculate overall score (weighted average)
  const overallScore = calculateOverallScore(metrics);

  // Store the score in the database
  await prisma.codeQualityScore.create({
    data: {
      pullRequestId,
      repositoryId,
      score: overallScore,
      ...metrics,
    },
  });

  return {
    metrics,
    suggestions: aiAnalysis.suggestions || [],
    issues: aiAnalysis.issues || [],
  };
}

function calculateComplexityScore(analysis: any): number {
  // Implement complexity scoring logic
  // This could consider cyclomatic complexity, nesting depth, etc.
  return 0; // Placeholder
}

function calculateTestCoverageScore(analysis: any): number {
  // Implement test coverage scoring logic
  // This could consider unit test coverage, integration test coverage, etc.
  return 0; // Placeholder
}

function calculateCodeStyleScore(analysis: any): number {
  // Implement code style scoring logic
  // This could consider formatting, naming conventions, etc.
  return 0; // Placeholder
}

function calculateSecurityScore(analysis: any): number {
  // Implement security scoring logic
  // This could consider security vulnerabilities, best practices, etc.
  return 0; // Placeholder
}

function calculateDocumentationScore(analysis: any): number {
  // Implement documentation scoring logic
  // This could consider inline comments, API documentation, etc.
  return 0; // Placeholder
}

function calculateOverallScore(metrics: CodeQualityMetrics): number {
  // Calculate weighted average of all metrics
  const weights = {
    codeComplexity: 0.25,
    testCoverage: 0.25,
    codeStyle: 0.15,
    securityScore: 0.20,
    documentationScore: 0.15,
  };

  return (
    metrics.codeComplexity * weights.codeComplexity +
    metrics.testCoverage * weights.testCoverage +
    metrics.codeStyle * weights.codeStyle +
    metrics.securityScore * weights.securityScore +
    metrics.documentationScore * weights.documentationScore
  );
}

export async function getQualityTrends(
  repositoryId: string,
  timeRange: { start: Date; end: Date }
) {
  return prisma.codeQualityScore.findMany({
    where: {
      repositoryId,
      createdAt: {
        gte: timeRange.start,
        lte: timeRange.end,
      },
    },
    orderBy: {
      createdAt: 'asc',
    },
  });
}

export async function getRepositoryAverageScore(repositoryId: string) {
  const scores = await prisma.codeQualityScore.findMany({
    where: { repositoryId },
    select: { score: true },
  });

  if (scores.length === 0) return 0;

  const sum = scores.reduce((acc, curr) => acc + curr.score, 0);
  return sum / scores.length;
}

export async function getUserAverageScore(userId: string) {
  const repositories = await prisma.monitoredRepository.findMany({
    where: { userId },
    select: { id: true },
  });

  const repositoryIds = repositories.map((repo) => repo.id);
  const scores = await prisma.codeQualityScore.findMany({
    where: {
      repositoryId: { in: repositoryIds },
    },
    select: { score: true },
  });

  if (scores.length === 0) return 0;

  const sum = scores.reduce((acc, curr) => acc + curr.score, 0);
  return sum / scores.length;
} 