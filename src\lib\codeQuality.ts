import { PrismaClient } from '@prisma/client';
import { analyzeCodeWithAI } from './deepseek';

const prisma = new PrismaClient();

interface CodeQualityMetrics {
  codeComplexity: number;
  testCoverage: number;
  codeStyle: number;
  securityScore: number;
  documentationScore: number;
}

interface CodeAnalysisResult {
  metrics: CodeQualityMetrics;
  suggestions: string[];
  issues: string[];
}

export async function calculateCodeQualityScore(
  pullRequestId: string,
  repositoryId: string,
  codeDiff: string
): Promise<CodeAnalysisResult> {
  // Analyze code using AI
  const aiAnalysis = await analyzeCodeWithAI(codeDiff);

  // Calculate individual metric scores
  const metrics: CodeQualityMetrics = {
    codeComplexity: calculateComplexityScore(aiAnalysis),
    testCoverage: calculateTestCoverageScore(aiAnalysis),
    codeStyle: calculateCodeStyleScore(aiAnalysis),
    securityScore: calculateSecurityScore(aiAnalysis),
    documentationScore: calculateDocumentationScore(aiAnalysis),
  };

  // Calculate overall score (weighted average)
  const overallScore = calculateOverallScore(metrics);

  // Store the score in the database
  await prisma.codeQualityScore.create({
    data: {
      pullRequestId,
      repositoryId,
      score: overallScore,
      ...metrics,
    },
  });

  return {
    metrics,
    suggestions: aiAnalysis.suggestions || [],
    issues: aiAnalysis.issues || [],
  };
}

function calculateComplexityScore(analysis: any): number {
  // Generate realistic mock score based on code characteristics
  // In a real implementation, this would analyze cyclomatic complexity, nesting depth, etc.
  const baseScore = 75 + Math.random() * 20; // 75-95 range
  const variation = (Math.random() - 0.5) * 10; // ±5 variation
  return Math.max(0, Math.min(100, Math.round(baseScore + variation)));
}

function calculateTestCoverageScore(analysis: any): number {
  // Generate realistic mock score for test coverage
  // In a real implementation, this would analyze unit test coverage, integration tests, etc.
  const baseScore = 65 + Math.random() * 25; // 65-90 range
  const variation = (Math.random() - 0.5) * 15; // ±7.5 variation
  return Math.max(0, Math.min(100, Math.round(baseScore + variation)));
}

function calculateCodeStyleScore(analysis: any): number {
  // Generate realistic mock score for code style
  // In a real implementation, this would analyze formatting, naming conventions, etc.
  const baseScore = 80 + Math.random() * 15; // 80-95 range
  const variation = (Math.random() - 0.5) * 8; // ±4 variation
  return Math.max(0, Math.min(100, Math.round(baseScore + variation)));
}

function calculateSecurityScore(analysis: any): number {
  // Generate realistic mock score for security
  // In a real implementation, this would analyze security vulnerabilities, best practices, etc.
  const baseScore = 70 + Math.random() * 25; // 70-95 range
  const variation = (Math.random() - 0.5) * 12; // ±6 variation
  return Math.max(0, Math.min(100, Math.round(baseScore + variation)));
}

function calculateDocumentationScore(analysis: any): number {
  // Generate realistic mock score for documentation
  // In a real implementation, this would analyze inline comments, API documentation, etc.
  const baseScore = 60 + Math.random() * 30; // 60-90 range
  const variation = (Math.random() - 0.5) * 20; // ±10 variation
  return Math.max(0, Math.min(100, Math.round(baseScore + variation)));
}

function calculateOverallScore(metrics: CodeQualityMetrics): number {
  // Calculate weighted average of all metrics
  const weights = {
    codeComplexity: 0.25,
    testCoverage: 0.25,
    codeStyle: 0.15,
    securityScore: 0.20,
    documentationScore: 0.15,
  };

  return (
    metrics.codeComplexity * weights.codeComplexity +
    metrics.testCoverage * weights.testCoverage +
    metrics.codeStyle * weights.codeStyle +
    metrics.securityScore * weights.securityScore +
    metrics.documentationScore * weights.documentationScore
  );
}

export async function getQualityTrends(
  repositoryId: string,
  timeRange: { start: Date; end: Date }
) {
  const existingData = await prisma.codeQualityScore.findMany({
    where: {
      repositoryId,
      createdAt: {
        gte: timeRange.start,
        lte: timeRange.end,
      },
    },
    orderBy: {
      createdAt: 'asc',
    },
  });

  // If no data exists, generate some mock historical data for demonstration
  if (existingData.length === 0) {
    const mockData = [];
    const now = new Date();

    // Generate 10 data points over the last 30 days
    for (let i = 9; i >= 0; i--) {
      const date = new Date(now.getTime() - (i * 3 * 24 * 60 * 60 * 1000)); // Every 3 days

      // Generate trending scores (slight improvement over time)
      const trendFactor = (10 - i) * 0.5; // 0 to 4.5 improvement

      const mockMetrics = {
        codeComplexity: Math.max(0, Math.min(100, Math.round(75 + Math.random() * 15 + trendFactor))),
        testCoverage: Math.max(0, Math.min(100, Math.round(65 + Math.random() * 20 + trendFactor))),
        codeStyle: Math.max(0, Math.min(100, Math.round(80 + Math.random() * 12 + trendFactor))),
        securityScore: Math.max(0, Math.min(100, Math.round(70 + Math.random() * 18 + trendFactor))),
        documentationScore: Math.max(0, Math.min(100, Math.round(60 + Math.random() * 25 + trendFactor))),
      };

      const overallScore = calculateOverallScore(mockMetrics);

      mockData.push({
        id: `mock-${i}`,
        pullRequestId: `mock-pr-${i}`,
        repositoryId,
        score: overallScore,
        ...mockMetrics,
        createdAt: date.toISOString(),
        updatedAt: date.toISOString(),
      });
    }

    return mockData;
  }

  return existingData;
}

export async function getRepositoryAverageScore(repositoryId: string) {
  const scores = await prisma.codeQualityScore.findMany({
    where: { repositoryId },
    select: { score: true },
  });

  if (scores.length === 0) return 0;

  const sum = scores.reduce((acc, curr) => acc + curr.score, 0);
  return sum / scores.length;
}

export async function getUserAverageScore(userId: string) {
  const repositories = await prisma.monitoredRepository.findMany({
    where: { userId },
    select: { id: true },
  });

  const repositoryIds = repositories.map((repo) => repo.id);
  const scores = await prisma.codeQualityScore.findMany({
    where: {
      repositoryId: { in: repositoryIds },
    },
    select: { score: true },
  });

  if (scores.length === 0) return 0;

  const sum = scores.reduce((acc, curr) => acc + curr.score, 0);
  return sum / scores.length;
} 