const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';
const DEEPSEEK_MODEL = 'deepseek/deepseek-chat-v3-0324';
const LLAMA4_MAVERICK_MODEL = 'meta-llama/llama-4-maverick';
const SITE_URL = process.env.NEXTAUTH_URL || 'http://localhost:3000';
const SITE_TITLE = 'Amazingly AI Code Reviewer';

// Generic OpenRouter chat completion function
export async function openrouterChatCompletion(
  messages: { role: 'user' | 'system' | 'assistant', content: string }[],
  model: string,
  extra?: Record<string, any>
) {
  if (!OPENROUTER_API_KEY) throw new Error('Missing OpenRouter API key');
  const res = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
      'HTTP-Referer': SITE_URL,
      'X-Title': SITE_TITLE,
    },
    body: JSON.stringify({
      model,
      messages,
      ...extra,
    }),
  });
  if (!res.ok) {
    const error = await res.text();
    throw new Error(`OpenRouter API error: ${error}`);
  }
  const data = await res.json();
  return data.choices?.[0]?.message?.content || '';
}

// Backward compatibility: DeepSeek only
export async function deepseekChatCompletion(
  messages: { role: 'user' | 'system' | 'assistant', content: string }[],
  extra?: Record<string, any>
) {
  return openrouterChatCompletion(messages, DEEPSEEK_MODEL, extra);
}

// Placeholder for AI code analysis
export async function analyzeCodeWithAI(diff: string) {
  // TODO: Replace with real AI analysis logic
  return {
    suggestions: [],
    issues: [],
    codeComplexity: 80,
    testCoverage: 70,
    codeStyle: 90,
    securityScore: 85,
    documentationScore: 75,
  };
}

export { DEEPSEEK_MODEL, LLAMA4_MAVERICK_MODEL }; 