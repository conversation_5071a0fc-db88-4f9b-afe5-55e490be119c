{"name": "amazingly-ai-cddereview", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@auth/core": "^0.34.2", "@prisma/client": "^6.8.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-slot": "^1.0.2", "@types/recharts": "^1.8.29", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "jspdf": "^3.0.1", "lucide-react": "^0.400.0", "next": "15.3.2", "next-auth": "^4.24.11", "prisma": "^6.8.2", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "recharts": "^2.15.3", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv-cli": "^8.0.0", "tailwindcss": "^4", "typescript": "^5"}}