"use client";
import { useEffect, useState } from "react";

export default function ReleaseNotesPage() {
  const [repos, setRepos] = useState<any[]>([]);
  const [selectedRepo, setSelectedRepo] = useState<string>("");
  const [prs, setPrs] = useState<any[]>([]);
  const [selectedPrs, setSelectedPrs] = useState<Set<number>>(new Set());
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [loadingPRs, setLoadingPRs] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [releaseNotes, setReleaseNotes] = useState("");
  const [error, setError] = useState<string | null>(null);

  // Fetch monitored repos
  useEffect(() => {
    fetch("/api/monitored-repos")
      .then(res => res.json())
      .then(data => setRepos(data || []));
  }, []);

  // Fetch merged PRs for selected repo and date range
  const fetchPRs = async () => {
    if (!selectedRepo) return;
    setLoadingPRs(true);
    setError(null);
    setPrs([]);
    setSelectedPrs(new Set());
    try {
      const repo = repos.find(r => r.id === selectedRepo);
      if (!repo) return;
      // Use your backend or GitHub API directly
      const url = `/api/github/merged-prs?owner=${repo.repoOwner}&repo=${repo.repoName}` +
        (startDate ? `&start=${startDate}` : "") + (endDate ? `&end=${endDate}` : "");
      const res = await fetch(url);
      const data = await res.json();
      if (res.ok) setPrs(data);
      else setError(data.error || "Failed to fetch PRs");
    } catch (err: any) {
      setError(err.message);
    }
    setLoadingPRs(false);
  };

  // Handle PR selection
  const togglePr = (number: number) => {
    setSelectedPrs(prev => {
      const next = new Set(prev);
      if (next.has(number)) next.delete(number);
      else next.add(number);
      return next;
    });
  };

  // Generate release notes
  const handleGenerate = async () => {
    setGenerating(true);
    setReleaseNotes("");
    setError(null);
    try {
      const selected = prs.filter((pr: any) => selectedPrs.has(pr.number));
      // Fetch diffs for each PR (could be optimized)
      const prsWithDiffs = await Promise.all(selected.map(async (pr: any) => {
        const res = await fetch(`/api/github/pr-diff?owner=${pr.base.repo.owner.login}&repo=${pr.base.repo.name}&number=${pr.number}`);
        const data = await res.json();
        return {
          title: pr.title,
          description: pr.body,
          diff: data.diff,
          labels: pr.labels?.map((l: any) => l.name) || [],
        };
      }));
      const res = await fetch("/api/ai/release-notes", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ prs: prsWithDiffs }),
      });
      const data = await res.json();
      if (res.ok && data.releaseNotes) setReleaseNotes(data.releaseNotes);
      else setError(data.error || "Failed to generate release notes");
    } catch (err: any) {
      setError(err.message);
    }
    setGenerating(false);
  };

  return (
    <div className="max-w-3xl mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8 text-white">AI-Generated Release Notes</h1>
      <div className="mb-6 bg-gray-800 rounded-lg p-6 shadow">
        <label className="block font-semibold mb-2 text-white">Repository</label>
        <select
          className="w-full border rounded p-2 mb-4 bg-gray-900 text-white"
          value={selectedRepo}
          onChange={e => setSelectedRepo(e.target.value)}
        >
          <option value="">Select a repository</option>
          {repos.map(r => (
            <option key={r.id} value={r.id}>{r.repoOwner}/{r.repoName}</option>
          ))}
        </select>
        <div className="flex gap-4 mb-4">
          <div>
            <label className="block font-semibold mb-1 text-white">Start Date</label>
            <input type="date" className="border rounded p-2 bg-gray-900 text-white" value={startDate} onChange={e => setStartDate(e.target.value)} />
          </div>
          <div>
            <label className="block font-semibold mb-1 text-white">End Date</label>
            <input type="date" className="border rounded p-2 bg-gray-900 text-white" value={endDate} onChange={e => setEndDate(e.target.value)} />
          </div>
          <button
            className="bg-blue-600 text-white px-4 py-2 rounded self-end hover:bg-blue-700 font-semibold mt-6"
            onClick={fetchPRs}
            disabled={!selectedRepo || loadingPRs}
          >
            {loadingPRs ? "Loading PRs..." : "Fetch PRs"}
          </button>
        </div>
        {prs.length > 0 && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2 text-white">Merged PRs</h2>
            <div className="border rounded divide-y divide-gray-700 bg-gray-900">
              {prs.map((pr: any) => (
                <label key={pr.number} className="flex items-center gap-2 p-2 cursor-pointer text-white hover:bg-gray-800 transition">
                  <input
                    type="checkbox"
                    checked={selectedPrs.has(pr.number)}
                    onChange={() => togglePr(pr.number)}
                  />
                  <span className="font-mono text-xs text-gray-400">#{pr.number}</span>
                  <span className="font-semibold">{pr.title}</span>
                  <span className="text-xs text-gray-500">({pr.merged_at?.slice(0, 10)})</span>
                </label>
              ))}
            </div>
          </div>
        )}
        <button
          className="bg-green-600 text-white px-6 py-2 rounded font-semibold hover:bg-green-700 mb-2 mt-2 w-full"
          onClick={handleGenerate}
          disabled={generating || selectedPrs.size === 0}
        >
          {generating ? "Generating..." : "Generate Release Notes"}
        </button>
        {error && <div className="text-red-400 mb-2">{error}</div>}
      </div>
      {releaseNotes && (
        <div className="bg-white rounded-lg shadow p-8 mt-8">
          <h2 className="text-2xl font-bold mb-4 text-gray-900">Release Notes</h2>
          <div className="prose max-w-none text-gray-900" dangerouslySetInnerHTML={{ __html: releaseNotes.replace(/\n/g, '<br/>') }} />
        </div>
      )}
    </div>
  );
} 