"use client";
import { useEffect, useState } from "react";
import {
  FileText,
  Calendar,
  GitBranch,
  Search,
  Download,
  Copy,
  Check,
  RefreshCw,
  Sparkles,
  Filter,
  GitMerge,
  Tag,
  Clock,
  User,
  Hash,
  AlertTriangle,
  CheckCircle,
  Loader2
} from "lucide-react";

export default function ReleaseNotesPage() {
  const [repos, setRepos] = useState<any[]>([]);
  const [selectedRepo, setSelectedRepo] = useState<string>("");
  const [prs, setPrs] = useState<any[]>([]);
  const [filteredPrs, setFilteredPrs] = useState<any[]>([]);
  const [selectedPrs, setSelectedPrs] = useState<Set<number>>(new Set());
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [loadingRepos, setLoadingRepos] = useState(true);
  const [loadingPRs, setLoadingPRs] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [releaseNotes, setReleaseNotes] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);

  // Fetch monitored repos
  useEffect(() => {
    setLoadingRepos(true);
    fetch("/api/monitored-repos")
      .then(res => res.json())
      .then(data => setRepos(data || []))
      .catch(err => setError("Failed to load repositories"))
      .finally(() => setLoadingRepos(false));
  }, []);

  // Filter PRs based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredPrs(prs);
    } else {
      setFilteredPrs(prs.filter((pr: any) =>
        pr.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pr.user?.login?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pr.number.toString().includes(searchTerm)
      ));
    }
  }, [prs, searchTerm]);

  // Fetch merged PRs for selected repo and date range
  const fetchPRs = async () => {
    if (!selectedRepo) return;
    setLoadingPRs(true);
    setError(null);
    setPrs([]);
    setFilteredPrs([]);
    setSelectedPrs(new Set());
    setSearchTerm("");
    try {
      const repo = repos.find(r => r.id === selectedRepo);
      if (!repo) return;
      // Use your backend or GitHub API directly
      const url = `/api/github/merged-prs?owner=${repo.repoOwner}&repo=${repo.repoName}` +
        (startDate ? `&start=${startDate}` : "") + (endDate ? `&end=${endDate}` : "");
      const res = await fetch(url);
      const data = await res.json();
      if (res.ok) {
        setPrs(data);
        setFilteredPrs(data);
      } else {
        setError(data.error || "Failed to fetch PRs");
      }
    } catch (err: any) {
      setError(err.message);
    }
    setLoadingPRs(false);
  };

  // Copy release notes to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(releaseNotes);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Select/deselect all PRs
  const toggleAllPrs = () => {
    if (selectedPrs.size === filteredPrs.length) {
      setSelectedPrs(new Set());
    } else {
      setSelectedPrs(new Set(filteredPrs.map((pr: any) => pr.number)));
    }
  };

  // Handle PR selection
  const togglePr = (number: number) => {
    setSelectedPrs(prev => {
      const next = new Set(prev);
      if (next.has(number)) next.delete(number);
      else next.add(number);
      return next;
    });
  };

  // Generate release notes
  const handleGenerate = async () => {
    setGenerating(true);
    setReleaseNotes("");
    setError(null);
    try {
      const selected = prs.filter((pr: any) => selectedPrs.has(pr.number));
      // Fetch diffs for each PR (could be optimized)
      const prsWithDiffs = await Promise.all(selected.map(async (pr: any) => {
        const res = await fetch(`/api/github/pr-diff?owner=${pr.base.repo.owner.login}&repo=${pr.base.repo.name}&number=${pr.number}`);
        const data = await res.json();
        return {
          title: pr.title,
          description: pr.body,
          diff: data.diff,
          labels: pr.labels?.map((l: any) => l.name) || [],
        };
      }));
      const res = await fetch("/api/ai/release-notes", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ prs: prsWithDiffs }),
      });
      const data = await res.json();
      if (res.ok && data.releaseNotes) setReleaseNotes(data.releaseNotes);
      else setError(data.error || "Failed to generate release notes");
    } catch (err: any) {
      setError(err.message);
    }
    setGenerating(false);
  };

  return (
    <main
      className="p-6"
      style={{backgroundColor: 'var(--background)', minHeight: '100vh'}}
    >
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <header className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-3">
            <FileText className="h-8 w-8" style={{color: 'var(--primary)'}} />
            <div>
              <h1 className="text-3xl font-bold" style={{color: 'var(--foreground)'}}>
                AI-Generated Release Notes
              </h1>
              <p className="text-sm mt-1" style={{color: 'var(--muted-foreground)'}}>
                Generate comprehensive release notes from merged pull requests
              </p>
            </div>
          </div>
        </header>

        {/* Configuration Section */}
        <section
          className="rounded-lg p-6 mb-8"
          style={{
            backgroundColor: 'var(--card)',
            border: '1px solid var(--border)'
          }}
        >
          <div className="flex items-center space-x-2 mb-6">
            <GitBranch className="h-5 w-5" style={{color: 'var(--primary)'}} />
            <h2 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
              Configuration
            </h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Repository Selection */}
            <div>
              <label className="block text-sm font-medium mb-2" style={{color: 'var(--foreground)'}}>
                Repository
              </label>
              <select
                value={selectedRepo}
                onChange={e => setSelectedRepo(e.target.value)}
                disabled={loadingRepos}
                className="w-full p-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-50"
                style={{
                  backgroundColor: 'var(--background)',
                  borderColor: 'var(--border)',
                  color: 'var(--foreground)'
                }}
              >
                <option value="">Select a repository</option>
                {repos.map(r => (
                  <option key={r.id} value={r.id}>{r.repoOwner}/{r.repoName}</option>
                ))}
              </select>
            </div>

            {/* Date Range */}
            <div>
              <label className="block text-sm font-medium mb-2" style={{color: 'var(--foreground)'}}>
                Start Date
              </label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4" style={{color: 'var(--muted-foreground)'}} />
                <input
                  type="date"
                  value={startDate}
                  onChange={e => setStartDate(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-50"
                  style={{
                    backgroundColor: 'var(--background)',
                    borderColor: 'var(--border)',
                    color: 'var(--foreground)'
                  }}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2" style={{color: 'var(--foreground)'}}>
                End Date
              </label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4" style={{color: 'var(--muted-foreground)'}} />
                <input
                  type="date"
                  value={endDate}
                  onChange={e => setEndDate(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-50"
                  style={{
                    backgroundColor: 'var(--background)',
                    borderColor: 'var(--border)',
                    color: 'var(--foreground)'
                  }}
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end mt-6">
            <button
              onClick={fetchPRs}
              disabled={!selectedRepo || loadingPRs}
              className="flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all hover:opacity-90 disabled:opacity-50"
              style={{
                backgroundColor: 'var(--primary)',
                color: 'var(--primary-foreground)'
              }}
            >
              {loadingPRs ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Loading PRs...</span>
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4" />
                  <span>Fetch PRs</span>
                </>
              )}
            </button>
          </div>
        </section>

        {/* Error Display */}
        {error && (
          <div
            className="rounded-lg p-4 mb-6 flex items-center space-x-3"
            style={{
              backgroundColor: 'var(--destructive)',
              color: 'var(--destructive-foreground)'
            }}
          >
            <AlertTriangle className="h-5 w-5" />
            <span>{error}</span>
          </div>
        )}

        {/* PR Selection Section */}
        {prs.length > 0 && (
          <section
            className="rounded-lg p-6 mb-8"
            style={{
              backgroundColor: 'var(--card)',
              border: '1px solid var(--border)'
            }}
          >
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-2">
                <GitMerge className="h-5 w-5" style={{color: 'var(--primary)'}} />
                <h2 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                  Merged Pull Requests ({filteredPrs.length})
                </h2>
              </div>
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4" style={{color: 'var(--muted-foreground)'}} />
                  <input
                    type="text"
                    placeholder="Search PRs..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-50 w-64"
                    style={{
                      backgroundColor: 'var(--background)',
                      borderColor: 'var(--border)',
                      color: 'var(--foreground)'
                    }}
                  />
                </div>
                <button
                  onClick={toggleAllPrs}
                  className="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm transition-colors hover:opacity-80"
                  style={{
                    backgroundColor: 'var(--secondary)',
                    color: 'var(--secondary-foreground)'
                  }}
                >
                  <Filter className="h-4 w-4" />
                  <span>{selectedPrs.size === filteredPrs.length ? 'Deselect All' : 'Select All'}</span>
                </button>
              </div>
            </div>

            <div className="space-y-2 max-h-96 overflow-y-auto">
              {filteredPrs.map((pr: any) => (
                <div
                  key={pr.number}
                  className="flex items-center space-x-4 p-4 rounded-lg border transition-all hover:shadow-sm cursor-pointer"
                  style={{
                    backgroundColor: 'var(--background)',
                    borderColor: 'var(--border)'
                  }}
                  onClick={() => togglePr(pr.number)}
                >
                  <input
                    type="checkbox"
                    checked={selectedPrs.has(pr.number)}
                    onChange={() => togglePr(pr.number)}
                    className="h-4 w-4"
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3 mb-1">
                      <span className="text-xs font-mono px-2 py-1 rounded" style={{
                        backgroundColor: 'var(--accent)',
                        color: 'var(--accent-foreground)'
                      }}>
                        #{pr.number}
                      </span>
                      <h4 className="font-medium truncate" style={{color: 'var(--foreground)'}}>
                        {pr.title}
                      </h4>
                    </div>
                    <div className="flex items-center space-x-4 text-sm" style={{color: 'var(--muted-foreground)'}}>
                      <div className="flex items-center space-x-1">
                        <User className="h-3 w-3" />
                        <span>{pr.user?.login}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{new Date(pr.merged_at).toLocaleDateString()}</span>
                      </div>
                      {pr.labels && pr.labels.length > 0 && (
                        <div className="flex items-center space-x-1">
                          <Tag className="h-3 w-3" />
                          <span>{pr.labels.slice(0, 2).map((l: any) => l.name).join(', ')}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex items-center justify-between pt-6 border-t mt-6" style={{borderColor: 'var(--border)'}}>
              <div className="text-sm" style={{color: 'var(--muted-foreground)'}}>
                {selectedPrs.size} of {filteredPrs.length} PRs selected
              </div>
              <button
                onClick={handleGenerate}
                disabled={generating || selectedPrs.size === 0}
                className="flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all hover:opacity-90 disabled:opacity-50"
                style={{
                  backgroundColor: 'var(--primary)',
                  color: 'var(--primary-foreground)'
                }}
              >
                {generating ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Generating...</span>
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4" />
                    <span>Generate Release Notes</span>
                  </>
                )}
              </button>
            </div>
          </section>
        )}

        {/* Release Notes Display */}
        {releaseNotes && (
          <section
            className="rounded-lg p-6"
            style={{
              backgroundColor: 'var(--card)',
              border: '1px solid var(--border)'
            }}
          >
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-400" />
                <h2 className="text-xl font-semibold" style={{color: 'var(--foreground)'}}>
                  Generated Release Notes
                </h2>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={copyToClipboard}
                  className="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm transition-colors hover:opacity-80"
                  style={{
                    backgroundColor: 'var(--secondary)',
                    color: 'var(--secondary-foreground)'
                  }}
                >
                  {copied ? (
                    <>
                      <Check className="h-4 w-4" />
                      <span>Copied!</span>
                    </>
                  ) : (
                    <>
                      <Copy className="h-4 w-4" />
                      <span>Copy</span>
                    </>
                  )}
                </button>
                <button
                  className="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm transition-colors hover:opacity-80"
                  style={{
                    backgroundColor: 'var(--secondary)',
                    color: 'var(--secondary-foreground)'
                  }}
                >
                  <Download className="h-4 w-4" />
                  <span>Download</span>
                </button>
              </div>
            </div>
            <div
              className="prose max-w-none p-6 rounded-lg"
              style={{
                backgroundColor: 'var(--background)',
                border: '1px solid var(--border)',
                color: 'var(--foreground)'
              }}
            >
              <div dangerouslySetInnerHTML={{ __html: releaseNotes.replace(/\n/g, '<br/>') }} />
            </div>
          </section>
        )}
      </div>
    </main>
  );
}