import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session || !session.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const { searchParams } = new URL(req.url);
  const auditId = searchParams.get('id');
  if (!auditId) {
    return NextResponse.json({ error: 'Missing auditId' }, { status: 400 });
  }
  const audit = await prisma.deepAudit.findUnique({ where: { id: auditId } });
  if (!audit || audit.userId !== session.user.id) {
    return NextResponse.json({ error: 'Not found or forbidden' }, { status: 404 });
  }
  return NextResponse.json({
    status: audit.status,
    progress: audit.progress,
    report: audit.report,
    chunkResults: audit.chunkResults,
    startedAt: audit.startedAt,
    completedAt: audit.completedAt,
    error: audit.error,
    repoName: audit.repoName,
    repoOwner: audit.repoOwner,
  });
} 