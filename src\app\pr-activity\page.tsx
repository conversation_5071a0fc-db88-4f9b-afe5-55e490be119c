'use client';

import ReactMarkdown from 'react-markdown';
import { useEffect, useState } from 'react';

export default function PRActivityPage() {
  const [pr, setPr] = useState<{ aiReview: string } | null>(null);

  useEffect(() => {
    const fetchPRData = async () => {
      try {
        const response = await fetch('/api/pr-activity');
        if (!response.ok) throw new Error('Failed to fetch PR data');
        const data = await response.json();
        setPr(data);
      } catch (error) {
        console.error('Error fetching PR data:', error);
      }
    };

    fetchPRData();
  }, []);

  return (
    <div>
      {/* Replace the plain text display of aiReview with ReactMarkdown */}
      {pr?.aiReview && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">AI Review</h3>
          <ReactMarkdown>{pr.aiReview}</ReactMarkdown>
        </div>
      )}
    </div>
  );
} 